# Hướng Dẫn Sử Dụng Postman Để Test API

## 1. <PERSON>ài Đặt và Chuẩn Bị

### 1.1 T<PERSON>i và cài đặt Postman

- T<PERSON>y cập: https://www.postman.com/downloads/
- Tải và cài đặt phiên bản phù hợp với hệ điều hành

### 1.2 Tạo Collection mới

1. Mở Postman
2. Click "New" → "Collection"
3. Đặt tên: "SHOPBANHANG API"
4. Thêm description: "API testing for shop management system"

### 1.3 Thiết lập Environment Variables

1. Click vào biểu tượng "Environment" (bánh răng)
2. Click "Add"
3. Tên environment: "SHOPBANHANG Local"
4. Thêm các variables:
   - `base_url`: `http://localhost:8080/api/v1`
   - `token`: (để trống, sẽ được set sau khi login)

## 2. Test Authentication APIs

### 2.1 Test Login API

**<PERSON><PERSON><PERSON> request mới:**

1. Click "Add request" trong collection
2. Tên: "Login"
3. Method: `POST`
4. URL: `{{base_url}}/auth/login`

**Headers:**

```
Content-Type: application/json
```

**Body (raw JSON):**

```json
{
    "username": "admin",
    "password": "admin123"
}
```

**Test Script (tab Tests):**

```javascript
pm.test("Status code is 200", function () {
    pm.response.to.have.status(200);
});

pm.test("Response has token", function () {
    var jsonData = pm.response.json();
    pm.expect(jsonData.success).to.be.true;
    pm.expect(jsonData.data.token).to.exist;
  
    // Save token to environment
    pm.environment.set("token", jsonData.data.token);
});
```

### 2.2 Test Register API

**Tạo request:**

1. Tên: "Register"
2. Method: `POST`
3. URL: `{{base_url}}/auth/register`

**Body (raw JSON):**

```json
{
    "username": "testuser",
    "fullname": "Test User",
    "password": "password123"
}
```

### 2.3 Test Get Profile API

**Tạo request:**

1. Tên: "Get Profile"
2. Method: `GET`
3. URL: `{{base_url}}/auth/profile`

**Headers:**

```
Authorization: Bearer {{token}}
```

## 3. Test Product APIs

### 3.1 Get All Products

**Tạo request:**

1. Tên: "Get All Products"
2. Method: `GET`
3. URL: `{{base_url}}/products`

**Query Parameters:**

- `page`: 1
- `limit`: 10
- `search`: (optional)
- `category_id`: (optional)

### 3.2 Get Single Product

**Tạo request:**

1. Tên: "Get Product by ID"
2. Method: `GET`
3. URL: `{{base_url}}/products/1`

### 3.3 Create Product (Admin required)

**Tạo request:**

1. Tên: "Create Product"
2. Method: `POST`
3. URL: `{{base_url}}/products`

**Headers:**

```
Authorization: Bearer {{token}}
```

**Body (form-data):**

- `name`: "Test Product"
- `description`: "This is a test product"
- `price`: 100000
- `category_id`: 1
- `image`: [Select file]

### 3.4 Update Product

**Tạo request:**

1. Tên: "Update Product"
2. Method: `PUT`
3. URL: `{{base_url}}/products/1`

**Headers:**

```
Authorization: Bearer {{token}}
Content-Type: application/json
```

**Body (raw JSON):**

```json
{
    "name": "Updated Product Name",
    "description": "Updated description",
    "price": 150000,
    "category_id": 1
}
```

### 3.5 Delete Product

**Tạo request:**

1. Tên: "Delete Product"
2. Method: `DELETE`
3. URL: `{{base_url}}/products/1`

**Headers:**

```
Authorization: Bearer {{token}}
```

## 4. Test Category APIs

### 4.1 Get All Categories

**Tạo request:**

1. Tên: "Get All Categories"
2. Method: `GET`
3. URL: `{{base_url}}/categories`

### 4.2 Create Category

**Tạo request:**

1. Tên: "Create Category"
2. Method: `POST`
3. URL: `{{base_url}}/categories`

**Headers:**

```
Authorization: Bearer {{token}}
Content-Type: application/json
```

**Body (raw JSON):**

```json
{
    "name": "Test Category",
    "description": "This is a test category"
}
```

### 4.3 Get Products by Category

**Tạo request:**

1. Tên: "Get Products by Category"
2. Method: `GET`
3. URL: `{{base_url}}/categories/1/products`

## 5. Test Order APIs

### 5.1 Create Order

**Tạo request:**

1. Tên: "Create Order"
2. Method: `POST`
3. URL: `{{base_url}}/orders`

**Headers:**

```
Content-Type: application/json
```

**Body (raw JSON):**

```json
{
    "customer_name": "John Doe",
    "customer_phone": "0123456789",
    "customer_address": "123 Main Street, Ho Chi Minh City",
    "items": [
        {
            "product_id": 1,
            "quantity": 2
        },
        {
            "product_id": 2,
            "quantity": 1
        }
    ]
}
```

### 5.2 Search Orders by Phone

**Tạo request:**

1. Tên: "Search Orders by Phone"
2. Method: `POST`
3. URL: `{{base_url}}/orders/search`

**Body (raw JSON):**

```json
{
    "phone": "0123456789"
}
```

### 5.3 Get All Orders (Admin)

**Tạo request:**

1. Tên: "Get All Orders"
2. Method: `GET`
3. URL: `{{base_url}}/orders`

**Headers:**

```
Authorization: Bearer {{token}}
```

### 5.4 Update Order Status

**Tạo request:**

1. Tên: "Update Order Status"
2. Method: `PUT`
3. URL: `{{base_url}}/orders/1`

**Headers:**

```
Authorization: Bearer {{token}}
Content-Type: application/json
```

**Body (raw JSON):**

```json
{
    "status": "processing"
}
```

## 6. Tips và Best Practices

### 6.1 Sử dụng Pre-request Scripts

Để tự động refresh token khi hết hạn:

```javascript
// Pre-request Script
const token = pm.environment.get("token");
if (!token) {
    console.log("No token found, please login first");
}
```

### 6.2 Sử dụng Tests để Validate Response

```javascript
pm.test("Status code is 200", function () {
    pm.response.to.have.status(200);
});

pm.test("Response time is less than 2000ms", function () {
    pm.expect(pm.response.responseTime).to.be.below(2000);
});

pm.test("Response has required fields", function () {
    var jsonData = pm.response.json();
    pm.expect(jsonData).to.have.property('success');
    pm.expect(jsonData).to.have.property('message');
    pm.expect(jsonData).to.have.property('data');
});
```

### 6.3 Organize Requests trong Folders

Tạo folders trong collection:

- Authentication
- Products
- Categories
- Orders
- Account Management

### 6.4 Export và Share Collection

1. Click vào collection name
2. Click "..." → "Export"
3. Chọn format và export
4. Share file với team members

## 7. Troubleshooting

### 7.1 CORS Issues

Nếu gặp lỗi CORS, kiểm tra:

- Server có set CORS headers đúng không
- URL có đúng không
- Method có được support không

### 7.2 Authentication Issues

- Kiểm tra token có được set trong environment không
- Token có hết hạn không
- Header Authorization có đúng format không

### 7.3 File Upload Issues

- Sử dụng form-data cho file upload
- Kiểm tra file size và type
- Đảm bảo server support multipart/form-data
