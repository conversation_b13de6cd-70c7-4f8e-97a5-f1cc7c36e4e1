<?php

class ApiProductController extends BaseApiController
{
    private $productModel;
    private $categoryModel;

    public function __construct()
    {
        parent::__construct();
        $this->productModel = new ProductModel($this->db);
        $this->categoryModel = new CategoryModel($this->db);
    }

    /**
     * Handle API requests
     */
    public function handleRequest($action = null, $id = null)
    {
        try {
            switch ($this->requestMethod) {
                case 'GET':
                    if ($id) {
                        $this->getProduct($id);
                    } else {
                        $this->getProducts();
                    }
                    break;
                case 'POST':
                    $this->createProduct();
                    break;
                case 'PUT':
                    if ($id) {
                        $this->updateProduct($id);
                    } else {
                        $this->sendError('ID sản phẩm là bắt buộc cho việc cập nhật', 400);
                    }
                    break;
                case 'DELETE':
                    if ($id) {
                        $this->deleteProduct($id);
                    } else {
                        $this->sendError('ID sản phẩm là bắt buộc cho việc xóa', 400);
                    }
                    break;
                default:
                    $this->sendError('Phương thức không được hỗ trợ', 405);
            }
        } catch (Exception $e) {
            $this->sendError('Lỗi server: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get all products with pagination
     */
    private function getProducts()
    {
        try {
            $pagination = $this->getPaginationParams();
            $categoryId = isset($_GET['category_id']) ? intval($_GET['category_id']) : null;
            $search = isset($_GET['search']) ? trim($_GET['search']) : null;

            // Get products with filters
            $products = $this->getProductsWithFilters($pagination, $categoryId, $search);
            
            // Get total count for pagination
            $totalCount = $this->getProductsCount($categoryId, $search);
            
            $response = [
                'products' => $products,
                'pagination' => [
                    'current_page' => $pagination['page'],
                    'per_page' => $pagination['limit'],
                    'total' => $totalCount,
                    'total_pages' => ceil($totalCount / $pagination['limit'])
                ]
            ];

            $this->sendSuccess($response, 'Lấy danh sách sản phẩm thành công');
        } catch (Exception $e) {
            $this->sendError('Lỗi khi lấy danh sách sản phẩm: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get products with filters
     */
    private function getProductsWithFilters($pagination, $categoryId = null, $search = null)
    {
        $query = "SELECT p.id, p.name, p.description, p.price, p.image, p.category_id, c.name as category_name
                  FROM product p
                  LEFT JOIN category c ON p.category_id = c.id
                  WHERE 1=1";
        
        $params = [];
        
        if ($categoryId) {
            $query .= " AND p.category_id = ?";
            $params[] = $categoryId;
        }
        
        if ($search) {
            $query .= " AND (p.name LIKE ? OR p.description LIKE ?)";
            $params[] = "%{$search}%";
            $params[] = "%{$search}%";
        }
        
        $query .= " ORDER BY p.id DESC LIMIT ? OFFSET ?";
        $params[] = $pagination['limit'];
        $params[] = $pagination['offset'];
        
        $stmt = $this->db->prepare($query);
        $stmt->execute($params);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get products count with filters
     */
    private function getProductsCount($categoryId = null, $search = null)
    {
        $query = "SELECT COUNT(*) as count FROM product p WHERE 1=1";
        $params = [];
        
        if ($categoryId) {
            $query .= " AND p.category_id = ?";
            $params[] = $categoryId;
        }
        
        if ($search) {
            $query .= " AND (p.name LIKE ? OR p.description LIKE ?)";
            $params[] = "%{$search}%";
            $params[] = "%{$search}%";
        }
        
        $stmt = $this->db->prepare($query);
        $stmt->execute($params);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return $result['count'];
    }

    /**
     * Get single product by ID
     */
    private function getProduct($id)
    {
        try {
            $product = $this->productModel->getProductById($id);
            
            if (!$product) {
                $this->sendError('Không tìm thấy sản phẩm', 404);
            }

            // Convert object to array for consistent API response
            $productArray = (array) $product;
            
            $this->sendSuccess($productArray, 'Lấy thông tin sản phẩm thành công');
        } catch (Exception $e) {
            $this->sendError('Lỗi khi lấy thông tin sản phẩm: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Create new product
     */
    private function createProduct()
    {
        // Check admin permission
        $this->requireAdmin();

        // Validate required fields
        $requiredFields = ['name', 'description', 'price', 'category_id'];
        $errors = $this->validateRequired($this->requestData, $requiredFields);
        
        if (!empty($errors)) {
            $this->sendError('Dữ liệu không hợp lệ', 400, $errors);
        }

        // Additional validation
        if (!is_numeric($this->requestData['price']) || $this->requestData['price'] < 0) {
            $this->sendError('Giá sản phẩm không hợp lệ', 400);
        }

        // Check if category exists
        $category = $this->categoryModel->getCategoryById($this->requestData['category_id']);
        if (!$category) {
            $this->sendError('Danh mục không tồn tại', 400);
        }

        try {
            // Handle file upload if present
            $image = '';
            if (isset($_FILES['image'])) {
                $image = $this->handleFileUpload('image');
            }

            $result = $this->productModel->addProduct(
                $this->requestData['name'],
                $this->requestData['description'],
                $this->requestData['price'],
                $this->requestData['category_id'],
                $image
            );

            if (is_array($result)) {
                // Validation errors from model
                $this->sendError('Dữ liệu không hợp lệ', 400, $result);
            }

            if ($result) {
                $this->sendSuccess([], 'Tạo sản phẩm thành công', 201);
            } else {
                $this->sendError('Lỗi khi tạo sản phẩm', 500);
            }
        } catch (Exception $e) {
            $this->sendError('Lỗi khi tạo sản phẩm: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Update existing product
     */
    private function updateProduct($id)
    {
        // Check admin permission
        $this->requireAdmin();

        // Check if product exists
        $existingProduct = $this->productModel->getProductById($id);
        if (!$existingProduct) {
            $this->sendError('Không tìm thấy sản phẩm', 404);
        }

        // Validate required fields
        $requiredFields = ['name', 'description', 'price', 'category_id'];
        $errors = $this->validateRequired($this->requestData, $requiredFields);
        
        if (!empty($errors)) {
            $this->sendError('Dữ liệu không hợp lệ', 400, $errors);
        }

        // Additional validation
        if (!is_numeric($this->requestData['price']) || $this->requestData['price'] < 0) {
            $this->sendError('Giá sản phẩm không hợp lệ', 400);
        }

        // Check if category exists
        $category = $this->categoryModel->getCategoryById($this->requestData['category_id']);
        if (!$category) {
            $this->sendError('Danh mục không tồn tại', 400);
        }

        try {
            // Handle file upload if present, otherwise keep existing image
            $image = $existingProduct->image;
            if (isset($_FILES['image'])) {
                $newImage = $this->handleFileUpload('image');
                if ($newImage) {
                    $image = $newImage;
                }
            }

            $result = $this->productModel->updateProduct(
                $id,
                $this->requestData['name'],
                $this->requestData['description'],
                $this->requestData['price'],
                $this->requestData['category_id'],
                $image
            );

            if ($result) {
                $this->sendSuccess([], 'Cập nhật sản phẩm thành công');
            } else {
                $this->sendError('Lỗi khi cập nhật sản phẩm', 500);
            }
        } catch (Exception $e) {
            $this->sendError('Lỗi khi cập nhật sản phẩm: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Delete product
     */
    private function deleteProduct($id)
    {
        // Check admin permission
        $this->requireAdmin();

        // Check if product exists
        $existingProduct = $this->productModel->getProductById($id);
        if (!$existingProduct) {
            $this->sendError('Không tìm thấy sản phẩm', 404);
        }

        try {
            $result = $this->productModel->deleteProduct($id);

            if ($result) {
                $this->sendSuccess([], 'Xóa sản phẩm thành công');
            } else {
                $this->sendError('Lỗi khi xóa sản phẩm', 500);
            }
        } catch (Exception $e) {
            $this->sendError('Lỗi khi xóa sản phẩm: ' . $e->getMessage(), 500);
        }
    }
}
