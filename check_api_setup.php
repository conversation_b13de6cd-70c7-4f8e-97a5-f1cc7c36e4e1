<?php
/**
 * API Setup Checker
 * Run this script to verify your API setup is correct
 */

echo "=== API Setup Checker ===\n\n";

// Check 1: PHP Version
echo "1. Checking PHP version...\n";
$phpVersion = phpversion();
echo "   PHP Version: $phpVersion\n";
if (version_compare($phpVersion, '7.4.0', '<')) {
    echo "   ❌ PHP 7.4+ required\n";
    $errors[] = "PHP version too old";
} else {
    echo "   ✅ PHP version OK\n";
}

// Check 2: Required Extensions
echo "\n2. Checking PHP extensions...\n";
$requiredExtensions = ['pdo', 'pdo_mysql', 'json', 'curl'];
foreach ($requiredExtensions as $ext) {
    if (extension_loaded($ext)) {
        echo "   ✅ $ext extension loaded\n";
    } else {
        echo "   ❌ $ext extension missing\n";
        $errors[] = "$ext extension missing";
    }
}

// Check 3: Configuration files
echo "\n3. Checking configuration files...\n";
$configFiles = [
    'app/config/config.php',
    'app/config/bootstrap.php',
    'app/config/database.php'
];

foreach ($configFiles as $file) {
    if (file_exists($file)) {
        echo "   ✅ $file exists\n";
    } else {
        echo "   ❌ $file missing\n";
        $errors[] = "$file missing";
    }
}

// Check 4: API files
echo "\n4. Checking API files...\n";
$apiFiles = [
    'api.php',
    'app/api/BaseApiController.php',
    'app/api/ApiProductController.php',
    'app/api/ApiCategoryController.php',
    'app/api/ApiOrderController.php',
    'app/api/ApiAccountController.php'
];

foreach ($apiFiles as $file) {
    if (file_exists($file)) {
        echo "   ✅ $file exists\n";
    } else {
        echo "   ❌ $file missing\n";
        $errors[] = "$file missing";
    }
}

// Check 5: .htaccess file
echo "\n5. Checking .htaccess file...\n";
if (file_exists('.htaccess')) {
    echo "   ✅ .htaccess exists\n";
    $htaccessContent = file_get_contents('.htaccess');
    if (strpos($htaccessContent, 'api/') !== false) {
        echo "   ✅ .htaccess contains API rules\n";
    } else {
        echo "   ❌ .htaccess missing API rules\n";
        $errors[] = ".htaccess missing API rules";
    }
} else {
    echo "   ❌ .htaccess missing\n";
    $errors[] = ".htaccess missing";
}

// Check 6: Database connection
echo "\n6. Checking database connection...\n";
try {
    require_once 'app/config/bootstrap.php';
    $db = (new Database())->getConnection();
    if ($db) {
        echo "   ✅ Database connection successful\n";
        
        // Check tables
        $tables = ['account', 'category', 'product', 'orders', 'order_details'];
        foreach ($tables as $table) {
            $stmt = $db->prepare("SHOW TABLES LIKE '$table'");
            $stmt->execute();
            if ($stmt->rowCount() > 0) {
                echo "   ✅ Table '$table' exists\n";
            } else {
                echo "   ❌ Table '$table' missing\n";
                $errors[] = "Table '$table' missing";
            }
        }
    } else {
        echo "   ❌ Database connection failed\n";
        $errors[] = "Database connection failed";
    }
} catch (Exception $e) {
    echo "   ❌ Database error: " . $e->getMessage() . "\n";
    $errors[] = "Database error: " . $e->getMessage();
}

// Check 7: Uploads directory
echo "\n7. Checking uploads directory...\n";
if (is_dir('uploads')) {
    echo "   ✅ uploads directory exists\n";
    if (is_writable('uploads')) {
        echo "   ✅ uploads directory is writable\n";
    } else {
        echo "   ❌ uploads directory is not writable\n";
        $errors[] = "uploads directory not writable";
    }
} else {
    echo "   ❌ uploads directory missing\n";
    if (mkdir('uploads', 0755, true)) {
        echo "   ✅ uploads directory created\n";
    } else {
        echo "   ❌ Failed to create uploads directory\n";
        $errors[] = "uploads directory missing and cannot create";
    }
}

// Check 8: Test API endpoint
echo "\n8. Testing API endpoint...\n";
if (function_exists('curl_init')) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost:8080/api/v1/categories');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "   ❌ API endpoint test failed: $error\n";
        echo "   💡 Make sure your server is running on http://localhost:8080\n";
    } elseif ($httpCode == 200) {
        echo "   ✅ API endpoint responding\n";
        $data = json_decode($response, true);
        if ($data && isset($data['success'])) {
            echo "   ✅ API response format correct\n";
        } else {
            echo "   ❌ API response format incorrect\n";
        }
    } else {
        echo "   ❌ API endpoint returned HTTP $httpCode\n";
        echo "   Response: $response\n";
    }
} else {
    echo "   ❌ cURL extension not available for testing\n";
}

// Summary
echo "\n" . str_repeat("=", 50) . "\n";
if (empty($errors)) {
    echo "🎉 All checks passed! Your API setup is ready.\n\n";
    echo "Next steps:\n";
    echo "1. Run: php create_admin.php (to create admin account)\n";
    echo "2. Run: php test_api.php (to test API endpoints)\n";
    echo "3. Import Postman collection for manual testing\n";
} else {
    echo "❌ Setup incomplete. Please fix the following issues:\n\n";
    foreach ($errors as $error) {
        echo "   • $error\n";
    }
    echo "\nRefer to DEPLOYMENT_GUIDE.md for detailed setup instructions.\n";
}

echo "\n" . str_repeat("=", 50) . "\n";
?>
