<?php

class BaseApiController
{
    protected $db;
    protected $requestMethod;
    protected $requestData;

    public function __construct()
    {
        $this->db = (new Database())->getConnection();
        $this->requestMethod = $_SERVER['REQUEST_METHOD'];
        $this->requestData = $this->getRequestData();
        
        // Set CORS headers
        $this->setCorsHeaders();
        
        // Handle preflight requests
        if ($this->requestMethod === 'OPTIONS') {
            http_response_code(200);
            exit();
        }
    }

    /**
     * Set CORS headers for API requests
     */
    protected function setCorsHeaders()
    {
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
        header('Content-Type: application/json; charset=UTF-8');
    }

    /**
     * Get request data based on request method
     */
    protected function getRequestData()
    {
        $data = [];
        
        switch ($this->requestMethod) {
            case 'GET':
                $data = $_GET;
                break;
            case 'POST':
            case 'PUT':
            case 'DELETE':
                $input = file_get_contents('php://input');
                if (!empty($input)) {
                    $data = json_decode($input, true);
                    if (json_last_error() !== JSON_ERROR_NONE) {
                        $data = [];
                    }
                }
                // Also include form data if available
                if (!empty($_POST)) {
                    $data = array_merge($data, $_POST);
                }
                break;
        }
        
        return $data;
    }

    /**
     * Send JSON response
     */
    protected function sendResponse($data, $statusCode = 200)
    {
        http_response_code($statusCode);
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit();
    }

    /**
     * Send error response
     */
    protected function sendError($message, $statusCode = 400, $errors = [])
    {
        $response = [
            'success' => false,
            'message' => $message,
            'status_code' => $statusCode
        ];
        
        if (!empty($errors)) {
            $response['errors'] = $errors;
        }
        
        $this->sendResponse($response, $statusCode);
    }

    /**
     * Send success response
     */
    protected function sendSuccess($data = [], $message = 'Success', $statusCode = 200)
    {
        $response = [
            'success' => true,
            'message' => $message,
            'status_code' => $statusCode,
            'data' => $data
        ];
        
        $this->sendResponse($response, $statusCode);
    }

    /**
     * Validate required fields
     */
    protected function validateRequired($data, $requiredFields)
    {
        $errors = [];
        
        foreach ($requiredFields as $field) {
            if (!isset($data[$field]) || empty(trim($data[$field]))) {
                $errors[$field] = "Trường {$field} là bắt buộc";
            }
        }
        
        return $errors;
    }

    /**
     * Get pagination parameters
     */
    protected function getPaginationParams()
    {
        $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
        $limit = isset($_GET['limit']) ? max(1, min(100, intval($_GET['limit']))) : 10;
        $offset = ($page - 1) * $limit;
        
        return [
            'page' => $page,
            'limit' => $limit,
            'offset' => $offset
        ];
    }

    /**
     * Check if user is authenticated (basic implementation)
     */
    protected function requireAuth()
    {
        $headers = getallheaders();
        $authHeader = isset($headers['Authorization']) ? $headers['Authorization'] : '';
        
        if (empty($authHeader) || !preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
            $this->sendError('Token xác thực không hợp lệ', 401);
        }
        
        $token = $matches[1];
        
        // Simple token validation - in production, use JWT or similar
        if (!$this->validateToken($token)) {
            $this->sendError('Token xác thực không hợp lệ', 401);
        }
        
        return $this->getUserFromToken($token);
    }

    /**
     * Validate token (basic implementation)
     */
    protected function validateToken($token)
    {
        // Basic implementation - in production, use proper JWT validation
        return !empty($token) && strlen($token) > 10;
    }

    /**
     * Get user from token (basic implementation)
     */
    protected function getUserFromToken($token)
    {
        // Basic implementation - in production, decode JWT and get user info
        return [
            'id' => 1,
            'username' => 'admin',
            'role' => 'admin'
        ];
    }

    /**
     * Check if user has admin role
     */
    protected function requireAdmin()
    {
        $user = $this->requireAuth();
        
        if ($user['role'] !== 'admin') {
            $this->sendError('Không có quyền truy cập', 403);
        }
        
        return $user;
    }

    /**
     * Handle file upload for API
     */
    protected function handleFileUpload($fileKey = 'image')
    {
        if (!isset($_FILES[$fileKey]) || $_FILES[$fileKey]['error'] !== UPLOAD_ERR_OK) {
            return null;
        }

        $file = $_FILES[$fileKey];
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        
        if (!in_array($file['type'], $allowedTypes)) {
            $this->sendError('Chỉ chấp nhận file ảnh (JPEG, PNG, GIF, WebP)');
        }

        $maxSize = 5 * 1024 * 1024; // 5MB
        if ($file['size'] > $maxSize) {
            $this->sendError('File ảnh không được vượt quá 5MB');
        }

        $uploadDir = UPLOAD_DIR;
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }

        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = uniqid() . '.' . $extension;
        $uploadPath = $uploadDir . '/' . $filename;

        if (move_uploaded_file($file['tmp_name'], $uploadPath)) {
            return $filename;
        }

        $this->sendError('Lỗi khi upload file');
    }
}
