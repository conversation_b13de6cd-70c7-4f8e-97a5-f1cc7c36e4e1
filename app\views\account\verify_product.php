<?php 
require_once 'app/helper/SessionHelper.php';
include 'app/views/shares/header.php'; 
?>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0"><i class="fas fa-shield-alt mr-2"></i>Xác thực danh tính</h4>
                </div>
                <div class="card-body">
                    <?php if (isset($_SESSION['flash'])): ?>
                        <div class="alert alert-<?php echo $_SESSION['flash']['type'] === 'error' ? 'danger' : 'success'; ?> alert-dismissible fade show" role="alert">
                            <?php echo $_SESSION['flash']['message']; ?>
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <?php unset($_SESSION['flash']); ?>
                    <?php endif; ?>

                    <p class="alert alert-info">
                        <i class="fas fa-info-circle mr-2"></i>
                        Vui lòng chọn sản phẩm mà bạn đã từng đặt mua để xác thực danh tính.
                    </p>

                    <form action="/shopbanhang/Account/verifyProduct" method="POST">
                        <input type="hidden" name="username" value="<?php echo htmlspecialchars($_SESSION['reset_username']); ?>">
                        
                        <div class="row">
                            <?php foreach ($products as $product): ?>
                                <div class="col-md-4 mb-4">
                                    <div class="card h-100">
                                        <img src="/shopbanhang/<?php echo htmlspecialchars($product['image']); ?>"
                                             class="card-img-top"
                                             alt="<?php echo htmlspecialchars($product['name']); ?>"
                                             style="height: 200px; object-fit: contain;">
                                        <div class="card-body">
                                            <h5 class="card-title"><?php echo htmlspecialchars($product['name']); ?></h5>
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" 
                                                       name="product_id" 
                                                       value="<?php echo $product['id']; ?>" 
                                                       id="product_<?php echo $product['id']; ?>" required>
                                                <label class="form-check-label" for="product_<?php echo $product['id']; ?>">
                                                    Chọn sản phẩm này
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>

                        <div class="text-center mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-check-circle mr-2"></i>Xác nhận
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'app/views/shares/footer.php'; ?>
