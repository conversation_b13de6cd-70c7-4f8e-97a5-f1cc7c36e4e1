<?php 
require_once 'app/helper/SessionHelper.php';
include 'app/views/shares/header.php'; ?>

<!-- Flash Messages -->
<?php if (isset($_SESSION['flash'])): ?>
    <div class="alert alert-<?php echo $_SESSION['flash']['type'] === 'error' ? 'danger' : 'success'; ?> alert-dismissible fade show" role="alert">
        <?php echo $_SESSION['flash']['message']; ?>
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <?php unset($_SESSION['flash']); ?>
<?php endif; ?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="mb-4">
    <ol class="breadcrumb bg-light py-2 px-3 rounded shadow-sm">
        <li class="breadcrumb-item"><a href="/shopbanhang/Product/"><i class="fas fa-home"></i> Trang chủ</a></li>
        <li class="breadcrumb-item active" aria-current="page">Chi tiết sản phẩm</li>
    </ol>
</nav>

<?php if ($product): ?>
    <div class="card shadow-lg border-0 mb-5">
        <div class="card-header text-center">
            <h2 class="mb-0 product-detail-title">Chi tiết sản phẩm</h2>
        </div>
        <div class="card-body p-0">
            <div class="row no-gutters">
                <div class="col-lg-6">
                    <div class="product-image-container p-4">
                        <?php if ($product->image): ?>
                            <img src="/shopbanhang/<?php echo htmlspecialchars($product->image, ENT_QUOTES, 'UTF-8'); ?>"
                                 class="img-fluid product-detail-img"
                                 alt="<?php echo htmlspecialchars($product->name, ENT_QUOTES, 'UTF-8'); ?>">
                        <?php else: ?>
                            <img src="/shopbanhang/assets/img/no-image.png"
                                 class="img-fluid product-detail-img"
                                 alt="Không có ảnh">
                        <?php endif; ?>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="p-4 p-lg-5">
                        <h1 class="product-detail-title">
                            <?php echo htmlspecialchars($product->name, ENT_QUOTES, 'UTF-8'); ?>
                        </h1>

                        <div class="product-detail-category">
                            <i class="fas fa-tag mr-1"></i>
                            <?php echo !empty($product->category_name)
                                ? htmlspecialchars($product->category_name, ENT_QUOTES, 'UTF-8')
                                : 'Chưa có danh mục'; ?>
                        </div>

                        <div class="product-detail-price mb-4">
                            <i class="fas fa-money-bill-wave mr-2"></i>
                            <?php echo number_format($product->price, 0, ',', '.'); ?> VND
                        </div>

                        <div class="mb-4">
                            <h5 class="mb-3"><i class="fas fa-info-circle mr-2"></i>Mô tả sản phẩm</h5>
                            <div class="p-3 bg-light rounded">
                                <?php echo nl2br(htmlspecialchars($product->description, ENT_QUOTES, 'UTF-8')); ?>
                            </div>
                        </div>                        <div class="d-flex flex-wrap mt-4">
                            <a href="/shopbanhang/Product/addToCart/<?php echo $product->id; ?>"
                               class="btn btn-success btn-lg mr-2 mb-2">
                                <i class="fas fa-cart-plus mr-2"></i>Thêm vào giỏ hàng
                            </a>
                            <a href="/shopbanhang/Product/list" class="btn btn-secondary btn-lg mb-2">
                                <i class="fas fa-arrow-left mr-2"></i>Quay lại danh sách
                            </a>
                        </div>

                        <?php if (SessionHelper::isAdmin()): ?>
                            <div class="mt-3">
                                <a href="/shopbanhang/Product/edit/<?php echo $product->id; ?>" class="btn btn-warning">
                                    <i class="fas fa-edit mr-1"></i>Sửa sản phẩm
                                </a>
                                <a href="/shopbanhang/Product/delete/<?php echo $product->id; ?>" 
                                   class="btn btn-danger"
                                   onclick="return confirm('Bạn có chắc chắn muốn xóa sản phẩm này?');">
                                    <i class="fas fa-trash-alt mr-1"></i>Xóa sản phẩm
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php else: ?>
    <div class="alert alert-danger text-center p-5 shadow-sm">
        <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
        <h4>Không tìm thấy sản phẩm!</h4>
        <p>Sản phẩm bạn đang tìm kiếm không tồn tại hoặc đã bị xóa.</p>
        <a href="/shopbanhang/Product/" class="btn btn-primary mt-3">
            <i class="fas fa-home mr-2"></i>Quay lại trang chủ
        </a>
    </div>
<?php endif; ?>

<?php include 'app/views/shares/footer.php'; ?>