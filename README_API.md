# SHOPBANHANG RESTful API

## Tổng Quan

Dự án này đã được mở rộng để hỗ trợ RESTful API hoàn chỉnh cho hệ thống quản lý cửa hàng bán hàng. API được xây dựng với PHP thuần và hỗ trợ các chức năng CRUD cho Products, Categories, Orders và Account management.

## Cấu Trúc Dự Án

```
SHOPBANHANGAPI/
├── app/
│   ├── api/                    # API Controllers
│   │   ├── BaseApiController.php
│   │   ├── ApiProductController.php
│   │   ├── ApiCategoryController.php
│   │   ├── ApiOrderController.php
│   │   └── ApiAccountController.php
│   ├── config/                 # Configuration files
│   ├── controllers/            # Web Controllers
│   ├── models/                 # Data Models
│   └── views/                  # View templates
├── api.php                     # API Router
├── index.php                   # Web Router
├── .htaccess                   # URL Rewriting rules
├── API_DOCUMENTATION.md        # Chi tiết API endpoints
├── POSTMAN_GUIDE.md           # Hướng dẫn test với Postman
├── test_api.php               # Script test API
└── README_API.md              # File này
```

## Tính Năng API

### 1. Authentication & Authorization
- ✅ User login/register
- ✅ JWT-like token authentication
- ✅ Role-based access control (Admin/User)
- ✅ Profile management
- ✅ Password change

### 2. Product Management
- ✅ CRUD operations cho products
- ✅ File upload cho product images
- ✅ Search và filter products
- ✅ Pagination support
- ✅ Category filtering

### 3. Category Management
- ✅ CRUD operations cho categories
- ✅ Get products by category
- ✅ Cascade delete protection

### 4. Order Management
- ✅ Create orders với multiple items
- ✅ Search orders by phone number
- ✅ Order status management
- ✅ User's order history
- ✅ Admin order management

### 5. Account Management
- ✅ User registration
- ✅ Admin user management
- ✅ Profile updates
- ✅ Password management

## Cài Đặt và Chạy

### 1. Yêu Cầu Hệ Thống
- PHP 7.4+
- MySQL 5.7+
- Apache/Nginx với mod_rewrite
- cURL extension

### 2. Cài Đặt
```bash
# Clone repository
git clone <repository-url>
cd SHOPBANHANGAPI

# Import database
mysql -u root -p < database.sql

# Cấu hình database trong app/config/config.php
# Hoặc sử dụng environment variables
```

### 3. Chạy với Docker (Recommended)
```bash
docker-compose up -d
```

### 4. Chạy với XAMPP/WAMP/Laragon
1. Copy project vào htdocs/www folder
2. Import database.sql
3. Truy cập http://localhost/SHOPBANHANGAPI

## API Base URL

```
http://localhost:8080/api/v1
```

## Quick Start

### 1. Test API với PHP Script
```bash
php test_api.php
```

### 2. Test với cURL

**Login:**
```bash
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'
```

**Get Products:**
```bash
curl -X GET http://localhost:8080/api/v1/products
```

**Create Product (Admin):**
```bash
curl -X POST http://localhost:8080/api/v1/products \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"name":"Test Product","description":"Test","price":100000,"category_id":1}'
```

### 3. Test với Postman
Xem chi tiết trong file `POSTMAN_GUIDE.md`

## API Documentation

Chi tiết đầy đủ về các endpoints có trong file `API_DOCUMENTATION.md`

### Các Endpoints Chính:

**Authentication:**
- `POST /auth/login` - Đăng nhập
- `POST /auth/register` - Đăng ký
- `GET /auth/profile` - Lấy profile
- `PUT /auth/profile` - Cập nhật profile

**Products:**
- `GET /products` - Lấy danh sách sản phẩm
- `GET /products/{id}` - Lấy sản phẩm theo ID
- `POST /products` - Tạo sản phẩm mới (Admin)
- `PUT /products/{id}` - Cập nhật sản phẩm (Admin)
- `DELETE /products/{id}` - Xóa sản phẩm (Admin)

**Categories:**
- `GET /categories` - Lấy danh sách danh mục
- `GET /categories/{id}` - Lấy danh mục theo ID
- `GET /categories/{id}/products` - Lấy sản phẩm theo danh mục
- `POST /categories` - Tạo danh mục (Admin)
- `PUT /categories/{id}` - Cập nhật danh mục (Admin)
- `DELETE /categories/{id}` - Xóa danh mục (Admin)

**Orders:**
- `GET /orders` - Lấy danh sách đơn hàng (Admin)
- `GET /orders/{id}` - Lấy đơn hàng theo ID
- `POST /orders` - Tạo đơn hàng mới
- `POST /orders/search` - Tìm đơn hàng theo SĐT
- `GET /orders/my-orders` - Lấy đơn hàng của user
- `PUT /orders/{id}` - Cập nhật trạng thái đơn hàng (Admin)

## Response Format

Tất cả API responses đều có format JSON chuẩn:

**Success:**
```json
{
    "success": true,
    "message": "Success message",
    "status_code": 200,
    "data": {}
}
```

**Error:**
```json
{
    "success": false,
    "message": "Error message",
    "status_code": 400,
    "errors": {}
}
```

## Security Features

- ✅ CORS headers configuration
- ✅ Input validation và sanitization
- ✅ SQL injection protection (PDO)
- ✅ XSS protection
- ✅ File upload validation
- ✅ Rate limiting ready (có thể implement)
- ✅ Token-based authentication

## Error Handling

API có error handling toàn diện với:
- HTTP status codes chuẩn
- Error messages bằng tiếng Việt
- Detailed error information
- Exception handling

## Performance Features

- ✅ Pagination cho large datasets
- ✅ Efficient database queries
- ✅ Proper indexing
- ✅ Caching ready (có thể implement Redis)

## Development Tools

1. **API Documentation:** `API_DOCUMENTATION.md`
2. **Postman Guide:** `POSTMAN_GUIDE.md`
3. **Test Script:** `test_api.php`
4. **Database Schema:** `database.sql`

## Troubleshooting

### Common Issues:

1. **404 Not Found:**
   - Kiểm tra .htaccess có hoạt động không
   - Kiểm tra mod_rewrite có enable không

2. **CORS Errors:**
   - Kiểm tra CORS headers trong .htaccess
   - Kiểm tra Origin domain

3. **Database Connection:**
   - Kiểm tra config trong app/config/config.php
   - Kiểm tra MySQL service có chạy không

4. **File Upload Issues:**
   - Kiểm tra permissions của uploads folder
   - Kiểm tra PHP upload_max_filesize

## Roadmap

### Planned Features:
- [ ] JWT implementation với proper library
- [ ] Rate limiting
- [ ] API versioning
- [ ] Caching layer (Redis)
- [ ] API analytics
- [ ] Webhook support
- [ ] OpenAPI/Swagger documentation

## Contributing

1. Fork the repository
2. Create feature branch
3. Make changes
4. Test thoroughly
5. Submit pull request

## License

This project is licensed under the MIT License.

## Support

Nếu gặp vấn đề, vui lòng:
1. Kiểm tra documentation
2. Chạy test script
3. Kiểm tra logs
4. Tạo issue với chi tiết lỗi
