<?php
require_once 'app/config/bootstrap.php';

$url = $_GET['url'] ?? '';
$url = rtrim($url, '/');
$url = filter_var($url, FILTER_SANITIZE_URL);
$url = explode('/', $url);

// Define default routes
$defaultController = 'ProductController';
$defaultAction = 'index';

// Map URLs to controllers
$urlMap = [
    '' => 'ProductController',
    'product' => 'ProductController',
    'category' => 'CategoryController',
    'account' => 'AccountController',
    'order' => 'OrderController'
];

// Map controllers to their valid actions
$actionMap = [
    'ProductController' => ['index', 'show', 'add', 'edit', 'delete', 'cart', 'checkout'],
    'CategoryController' => ['index', 'show', 'add', 'edit', 'delete'],
    'AccountController' => ['index', 'login', 'register', 'profile', 'logout'],
    'OrderController' => ['index', 'show', 'create', 'manage', 'myOrders']
];

// Determine controller from URL
$requestedController = strtolower($url[0] ?? '');
$controllerName = $urlMap[$requestedController] ?? $defaultController;
$controllerFile = APP_ROOT . '/controllers/' . $controllerName . '.php';

// Determine action
$requestedAction = strtolower($url[1] ?? '');
// Use the requested action if it exists in the actionMap for this controller, otherwise use default
$action = isset($actionMap[$controllerName]) && in_array($requestedAction, $actionMap[$controllerName]) 
    ? $requestedAction 
    : $defaultAction;

// die ("controller=$controllerName - action=$action");
// Kiểm tra xem controller và action có tồn tại không
if (!file_exists($controllerFile)) {
    die('Controller not found: ' . $controllerFile);
}

require_once $controllerFile;
$controller = new $controllerName();

if (!method_exists($controller, $action)) {
    die('Action not found: ' . $action);
}
// Gọi action với các tham số còn lại (nếu có)
call_user_func_array([$controller, $action], array_slice($url, 2));