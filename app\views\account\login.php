<?php include 'app/views/shares/header.php'; ?>

<!-- Flash Messages -->
<?php if (isset($_SESSION['flash'])): ?>
    <div class="alert alert-<?php echo $_SESSION['flash']['type'] === 'error' ? 'danger' : 'success'; ?> alert-dismissible fade show" role="alert">
        <?php echo $_SESSION['flash']['message']; ?>
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <?php unset($_SESSION['flash']); ?>
<?php endif; ?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/shopbanhang/Product/">Trang chủ</a></li>
        <li class="breadcrumb-item active" aria-current="page">Đăng nhập</li>
    </ol>
</nav>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="card shadow">
                <div class="card-header text-center bg-success text-white">
                    <h3 class="mb-0">
                        <i class="fas fa-sign-in-alt mr-2"></i>
                        Đăng nhập
                    </h3>
                </div>
                <div class="card-body p-4">
                    <form method="POST" action="/shopbanhang/Account/checkLogin" class="needs-validation" novalidate>
                        <!-- Username -->
                        <div class="form-group">
                            <label for="username">Username <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text"><i class="fas fa-user"></i></span>
                                </div>
                                <input type="text" 
                                       class="form-control" 
                                       id="username" 
                                       name="username" 
                                       required 
                                       value="<?php echo htmlspecialchars($_SESSION['form_data']['username'] ?? ''); ?>"
                                       placeholder="Nhập username"
                                       autofocus>
                                <div class="invalid-feedback">
                                    Vui lòng nhập username.
                                </div>
                            </div>
                        </div>

                        <!-- Password -->
                        <div class="form-group">
                            <label for="password">Mật khẩu <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                </div>
                                <input type="password" 
                                       class="form-control" 
                                       id="password" 
                                       name="password" 
                                       required 
                                       placeholder="Nhập mật khẩu">
                                <div class="input-group-append">
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="invalid-feedback">
                                    Vui lòng nhập mật khẩu.
                                </div>
                            </div>
                        </div>

                        <!-- Remember Me -->
                        <div class="form-group">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="remember_me" name="remember_me">
                                <label class="form-check-label" for="remember_me">
                                    Ghi nhớ đăng nhập
                                </label>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="form-group mb-0">
                            <button type="submit" class="btn btn-success btn-block btn-lg">
                                <i class="fas fa-sign-in-alt mr-2"></i>Đăng nhập
                            </button>
                        </div>
                    </form>
                </div>
                <div class="card-footer">
                    <div class="text-center">
                        <p class="mb-2">
                            <a href="/shopbanhang/Account/forgotPassword" class="text-danger">
                                <i class="fas fa-key mr-1"></i>Quên mật khẩu?
                            </a>
                        </p>
                        <p class="mb-2">
                            Chưa có tài khoản? 
                            <a href="/shopbanhang/Account/register" class="text-primary font-weight-bold">Đăng ký ngay</a>
                        </p>
                        <small class="text-muted">
                            <a href="/shopbanhang/Product/" class="text-decoration-none">
                                <i class="fas fa-arrow-left mr-1"></i>Về trang chủ
                            </a>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for form validation and password toggle -->
<script>
(function() {
    'use strict';
    
    // Bootstrap validation
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
    
    // Password toggle
    document.getElementById('togglePassword').addEventListener('click', function() {
        var passwordField = document.getElementById('password');
        var icon = this.querySelector('i');
        
        if (passwordField.type === 'password') {
            passwordField.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            passwordField.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    });
    
    // Quick login buttons for demo
    function quickLogin(username, password) {
        document.getElementById('username').value = username;
        document.getElementById('password').value = password;
    }
    
    // Add quick login functionality to demo info
    document.addEventListener('DOMContentLoaded', function() {
        var demoCard = document.querySelector('.card:last-child .card-body');
        if (demoCard) {
            demoCard.addEventListener('click', function(e) {
                if (e.target.closest('.col-6:first-child')) {
                    quickLogin('admin', 'admin123');
                } else if (e.target.closest('.col-6:last-child')) {
                    quickLogin('user1', 'user123');
                }
            });
            
            // Add cursor pointer style
            demoCard.style.cursor = 'pointer';
            demoCard.title = 'Nhấp để điền tự động';
        }
    });
})();
</script>

<?php 
// Xóa form data sau khi hiển thị
unset($_SESSION['form_data']); 
include 'app/views/shares/footer.php'; 
?>
