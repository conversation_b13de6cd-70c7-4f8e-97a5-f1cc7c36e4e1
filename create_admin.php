<?php
/**
 * <PERSON><PERSON>t to create default admin account
 * Run this script once to create the admin account for API testing
 */

require_once 'app/config/bootstrap.php';

try {
    $db = (new Database())->getConnection();
    
    if (!$db) {
        die("Cannot connect to database. Please check your database configuration.\n");
    }
    
    echo "Creating default admin account...\n";
    
    // Check if admin account already exists
    $checkQuery = "SELECT COUNT(*) as count FROM account WHERE username = 'admin'";
    $stmt = $db->prepare($checkQuery);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result['count'] > 0) {
        echo "Admin account already exists!\n";
        
        // Update password to ensure it's correct
        $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $updateQuery = "UPDATE account SET password = ? WHERE username = 'admin'";
        $stmt = $db->prepare($updateQuery);
        $stmt->execute([$hashedPassword]);
        
        echo "Admin password updated to: admin123\n";
    } else {
        // Create admin account
        $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $insertQuery = "INSERT INTO account (username, fullname, password, role, created_at) VALUES (?, ?, ?, ?, NOW())";
        $stmt = $db->prepare($insertQuery);
        $result = $stmt->execute(['admin', 'Administrator', $hashedPassword, 'admin']);
        
        if ($result) {
            echo "Admin account created successfully!\n";
        } else {
            echo "Failed to create admin account.\n";
            exit(1);
        }
    }
    
    // Also create a test user account
    $checkUserQuery = "SELECT COUNT(*) as count FROM account WHERE username = 'testuser'";
    $stmt = $db->prepare($checkUserQuery);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result['count'] == 0) {
        $hashedPassword = password_hash('password123', PASSWORD_DEFAULT);
        $insertQuery = "INSERT INTO account (username, fullname, password, role, created_at) VALUES (?, ?, ?, ?, NOW())";
        $stmt = $db->prepare($insertQuery);
        $result = $stmt->execute(['testuser', 'Test User', $hashedPassword, 'user']);
        
        if ($result) {
            echo "Test user account created successfully!\n";
        }
    } else {
        echo "Test user account already exists!\n";
    }
    
    echo "\nDefault accounts:\n";
    echo "Admin - Username: admin, Password: admin123\n";
    echo "User  - Username: testuser, Password: password123\n";
    echo "\nYou can now test the API!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
