<?php

require_once('app/models/OrderModel.php');
require_once 'app/helper/SessionHelper.php';

class OrderController {
    private $orderModel;

    public function __construct() {
        $this->orderModel = new OrderModel();
    }

    private function requireAdmin() {
        if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'admin') {
            $_SESSION['flash'] = [
                'type' => 'error',
                'message' => 'Bạn không có quyền thực hiện thao tác này'
            ];
            header('Location: /shopbanhang/Product/');
            exit;
        }
    }

    public function manage() {
        $this->requireAdmin();
        
        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        $orders = $this->orderModel->getAllOrders($page);
        
        include 'app/views/order/manage.php';
    }    public function detail($id) {
        if (SessionHelper::isAdmin()) {
            $this->requireAdmin();
        } else {
            // Yêu cầu đăng nhập với người dùng thường
            if (!isset($_SESSION['user'])) {
                $_SESSION['flash'] = [
                    'type' => 'error',
                    'message' => 'Vui lòng đăng nhập để xem chi tiết đơn hàng'
                ];
                header('Location: /shopbanhang/Account/login');
                return;
            }
        }
        
        $order = $this->orderModel->getOrderById($id);
        if (!$order) {
            $_SESSION['flash'] = [
                'type' => 'error',
                'message' => 'Không tìm thấy đơn hàng'
            ];            $redirectTo = SessionHelper::isAdmin() ? 
                         '/shopbanhang/Order/manage' : '/shopbanhang/Order/myOrders';
            header('Location: ' . $redirectTo);
            return;
        }
          // Với người dùng thường, chỉ cho phép xem đơn hàng của họ
        if (!SessionHelper::isAdmin()) {
            if ($order['user_id'] !== $_SESSION['user']['id']) {
                $_SESSION['flash'] = [
                    'type' => 'error',
                    'message' => 'Bạn không có quyền xem đơn hàng này'
                ];
                header('Location: /shopbanhang/Order/myOrders');
                return;
            }
        }
        
        include 'app/views/order/detail.php';
    }    public function updateStatus() {
        $this->requireAdmin();
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $orderId = $_POST['order_id'] ?? null;
            $status = $_POST['status'] ?? null;
            $returnTo = $_POST['return_to'] ?? 'manage';
            
            if ($orderId && $status) {
                $success = $this->orderModel->updateOrderStatus($orderId, $status);
                
                if ($success) {
                    $_SESSION['flash'] = [
                        'type' => 'success',
                        'message' => 'Đã cập nhật trạng thái đơn hàng'
                    ];
                } else {
                    $_SESSION['flash'] = [
                        'type' => 'error',
                        'message' => 'Không thể cập nhật trạng thái đơn hàng'
                    ];
                }
            }
        }
        
        if ($returnTo === 'detail') {
            header("Location: /shopbanhang/Order/detail/{$orderId}");
        } else {
            header('Location: /shopbanhang/Order/manage');
        }
    }    public function myOrders() {
        // Yêu cầu đăng nhập
        if (!isset($_SESSION['user'])) {
            $_SESSION['flash'] = [
                'type' => 'error',
                'message' => 'Vui lòng đăng nhập để xem đơn hàng của bạn'
            ];
            header('Location: /shopbanhang/Account/login');
            return;
        }
        
        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        
        try {
            $orders = $this->orderModel->getOrdersByUserId($_SESSION['user']['id'], $page);
            if (empty($orders['orders']) && $page === 1) {
                $_SESSION['flash'] = [
                    'type' => 'info',
                    'message' => 'Bạn chưa có đơn hàng nào'
                ];
            }
            include 'app/views/order/my_orders.php';
        } catch (Exception $e) {
            $_SESSION['flash'] = [
                'type' => 'error',
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ];
            include 'app/views/order/my_orders.php';
        }
    }
}
