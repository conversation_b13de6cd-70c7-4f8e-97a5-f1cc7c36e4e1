<?php

class ApiAccountController extends BaseApiController
{
    private $accountModel;

    public function __construct()
    {
        parent::__construct();
        $this->accountModel = new AccountModel();
    }

    /**
     * Handle API requests
     */
    public function handleRequest($action = null, $id = null)
    {
        try {
            switch ($this->requestMethod) {
                case 'GET':
                    if ($action === 'profile') {
                        $this->getProfile();
                    } elseif ($id) {
                        $this->getAccount($id);
                    } else {
                        $this->getAccounts();
                    }
                    break;
                case 'POST':
                    if ($action === 'login') {
                        $this->login();
                    } elseif ($action === 'register') {
                        $this->register();
                    } elseif ($action === 'refresh') {
                        $this->refreshToken();
                    } else {
                        $this->createAccount();
                    }
                    break;
                case 'PUT':
                    if ($action === 'profile') {
                        $this->updateProfile();
                    } elseif ($action === 'password') {
                        $this->changePassword();
                    } elseif ($id) {
                        $this->updateAccount($id);
                    } else {
                        $this->sendError('ID tài khoản là bắt buộc cho việc cập nhật', 400);
                    }
                    break;
                case 'DELETE':
                    if ($id) {
                        $this->deleteAccount($id);
                    } else {
                        $this->sendError('ID tài khoản là bắt buộc cho việc xóa', 400);
                    }
                    break;
                default:
                    $this->sendError('Phương thức không được hỗ trợ', 405);
            }
        } catch (Exception $e) {
            $this->sendError('Lỗi server: ' . $e->getMessage(), 500);
        }
    }

    /**
     * User login
     */
    private function login()
    {
        // Validate required fields
        $requiredFields = ['username', 'password'];
        $errors = $this->validateRequired($this->requestData, $requiredFields);
        
        if (!empty($errors)) {
            $this->sendError('Dữ liệu không hợp lệ', 400, $errors);
        }

        try {
            $account = $this->accountModel->authenticate(
                $this->requestData['username'],
                $this->requestData['password']
            );

            if (!$account) {
                $this->sendError('Tên đăng nhập hoặc mật khẩu không đúng', 401);
            }

            // Generate token (simple implementation)
            $token = $this->generateToken($account);

            $response = [
                'user' => $account,
                'token' => $token,
                'expires_in' => JWT_EXPIRATION
            ];

            $this->sendSuccess($response, 'Đăng nhập thành công');
        } catch (Exception $e) {
            $this->sendError('Lỗi khi đăng nhập: ' . $e->getMessage(), 500);
        }
    }

    /**
     * User registration
     */
    private function register()
    {
        // Validate required fields
        $requiredFields = ['username', 'fullname', 'password'];
        $errors = $this->validateRequired($this->requestData, $requiredFields);
        
        if (!empty($errors)) {
            $this->sendError('Dữ liệu không hợp lệ', 400, $errors);
        }

        // Additional validation
        if (strlen($this->requestData['password']) < 6) {
            $this->sendError('Mật khẩu phải có ít nhất 6 ký tự', 400);
        }

        try {
            $result = $this->accountModel->save(
                $this->requestData['username'],
                $this->requestData['fullname'],
                $this->requestData['password'],
                'user' // Default role
            );

            if ($result) {
                // Get the created account
                $account = $this->accountModel->getAccountByUsername($this->requestData['username']);
                unset($account['password']); // Remove password from response

                // Generate token
                $token = $this->generateToken($account);

                $response = [
                    'user' => $account,
                    'token' => $token,
                    'expires_in' => JWT_EXPIRATION
                ];

                $this->sendSuccess($response, 'Đăng ký thành công', 201);
            } else {
                $this->sendError('Lỗi khi tạo tài khoản', 500);
            }
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Username đã tồn tại') !== false) {
                $this->sendError('Tên đăng nhập đã tồn tại', 409);
            } else {
                $this->sendError('Lỗi khi đăng ký: ' . $e->getMessage(), 500);
            }
        }
    }

    /**
     * Get user profile (authenticated user only)
     */
    private function getProfile()
    {
        $user = $this->requireAuth();

        try {
            $account = $this->accountModel->getAccountByUsername($user['username']);
            
            if (!$account) {
                $this->sendError('Không tìm thấy thông tin tài khoản', 404);
            }

            unset($account['password']); // Remove password from response
            $this->sendSuccess($account, 'Lấy thông tin profile thành công');
        } catch (Exception $e) {
            $this->sendError('Lỗi khi lấy thông tin profile: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Update user profile (authenticated user only)
     */
    private function updateProfile()
    {
        $user = $this->requireAuth();

        // Validate required fields
        $requiredFields = ['fullname'];
        $errors = $this->validateRequired($this->requestData, $requiredFields);
        
        if (!empty($errors)) {
            $this->sendError('Dữ liệu không hợp lệ', 400, $errors);
        }

        try {
            // Update account info
            $query = "UPDATE account SET fullname = ? WHERE username = ?";
            $stmt = $this->db->prepare($query);
            $result = $stmt->execute([$this->requestData['fullname'], $user['username']]);

            if ($result) {
                $this->sendSuccess([], 'Cập nhật profile thành công');
            } else {
                $this->sendError('Lỗi khi cập nhật profile', 500);
            }
        } catch (Exception $e) {
            $this->sendError('Lỗi khi cập nhật profile: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Change password (authenticated user only)
     */
    private function changePassword()
    {
        $user = $this->requireAuth();

        // Validate required fields
        $requiredFields = ['current_password', 'new_password'];
        $errors = $this->validateRequired($this->requestData, $requiredFields);
        
        if (!empty($errors)) {
            $this->sendError('Dữ liệu không hợp lệ', 400, $errors);
        }

        // Additional validation
        if (strlen($this->requestData['new_password']) < 6) {
            $this->sendError('Mật khẩu mới phải có ít nhất 6 ký tự', 400);
        }

        try {
            // Verify current password
            $account = $this->accountModel->authenticate($user['username'], $this->requestData['current_password']);
            if (!$account) {
                $this->sendError('Mật khẩu hiện tại không đúng', 400);
            }

            // Update password
            $result = $this->accountModel->resetPassword($user['username'], $this->requestData['new_password']);

            if ($result) {
                $this->sendSuccess([], 'Đổi mật khẩu thành công');
            } else {
                $this->sendError('Lỗi khi đổi mật khẩu', 500);
            }
        } catch (Exception $e) {
            $this->sendError('Lỗi khi đổi mật khẩu: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get all accounts (admin only)
     */
    private function getAccounts()
    {
        // Check admin permission
        $this->requireAdmin();

        try {
            $pagination = $this->getPaginationParams();
            $result = $this->accountModel->getAllAccounts($pagination['page'], $pagination['limit']);

            $response = [
                'accounts' => $result['accounts'],
                'pagination' => [
                    'current_page' => $result['page'],
                    'per_page' => $result['limit'],
                    'total' => $result['total'],
                    'total_pages' => $result['total_pages']
                ]
            ];

            $this->sendSuccess($response, 'Lấy danh sách tài khoản thành công');
        } catch (Exception $e) {
            $this->sendError('Lỗi khi lấy danh sách tài khoản: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get single account by ID (admin only)
     */
    private function getAccount($id)
    {
        // Check admin permission
        $this->requireAdmin();

        try {
            $query = "SELECT id, username, fullname, role, created_at FROM account WHERE id = ?";
            $stmt = $this->db->prepare($query);
            $stmt->execute([$id]);
            $account = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$account) {
                $this->sendError('Không tìm thấy tài khoản', 404);
            }

            $this->sendSuccess($account, 'Lấy thông tin tài khoản thành công');
        } catch (Exception $e) {
            $this->sendError('Lỗi khi lấy thông tin tài khoản: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Create new account (admin only)
     */
    private function createAccount()
    {
        // Check admin permission
        $this->requireAdmin();

        // Validate required fields
        $requiredFields = ['username', 'fullname', 'password', 'role'];
        $errors = $this->validateRequired($this->requestData, $requiredFields);
        
        if (!empty($errors)) {
            $this->sendError('Dữ liệu không hợp lệ', 400, $errors);
        }

        // Additional validation
        if (strlen($this->requestData['password']) < 6) {
            $this->sendError('Mật khẩu phải có ít nhất 6 ký tự', 400);
        }

        $allowedRoles = ['admin', 'user'];
        if (!in_array($this->requestData['role'], $allowedRoles)) {
            $this->sendError('Vai trò không hợp lệ', 400);
        }

        try {
            $result = $this->accountModel->save(
                $this->requestData['username'],
                $this->requestData['fullname'],
                $this->requestData['password'],
                $this->requestData['role']
            );

            if ($result) {
                $this->sendSuccess([], 'Tạo tài khoản thành công', 201);
            } else {
                $this->sendError('Lỗi khi tạo tài khoản', 500);
            }
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Username đã tồn tại') !== false) {
                $this->sendError('Tên đăng nhập đã tồn tại', 409);
            } else {
                $this->sendError('Lỗi khi tạo tài khoản: ' . $e->getMessage(), 500);
            }
        }
    }

    /**
     * Generate simple token (in production, use JWT)
     */
    private function generateToken($account)
    {
        $payload = [
            'id' => $account['id'],
            'username' => $account['username'],
            'role' => $account['role'],
            'exp' => time() + JWT_EXPIRATION
        ];

        // Simple token generation - in production, use proper JWT library
        return base64_encode(json_encode($payload));
    }

    /**
     * Update account (admin only)
     */
    private function updateAccount($id)
    {
        // Check admin permission
        $this->requireAdmin();

        // Check if account exists
        $existingAccount = $this->getAccountById($id);
        if (!$existingAccount) {
            $this->sendError('Không tìm thấy tài khoản', 404);
        }

        // Validate required fields
        $requiredFields = ['fullname', 'role'];
        $errors = $this->validateRequired($this->requestData, $requiredFields);

        if (!empty($errors)) {
            $this->sendError('Dữ liệu không hợp lệ', 400, $errors);
        }

        $allowedRoles = ['admin', 'user'];
        if (!in_array($this->requestData['role'], $allowedRoles)) {
            $this->sendError('Vai trò không hợp lệ', 400);
        }

        try {
            // Update account info
            $query = "UPDATE account SET fullname = ?, role = ? WHERE id = ?";
            $stmt = $this->db->prepare($query);
            $result = $stmt->execute([
                $this->requestData['fullname'],
                $this->requestData['role'],
                $id
            ]);

            if ($result) {
                $this->sendSuccess([], 'Cập nhật tài khoản thành công');
            } else {
                $this->sendError('Lỗi khi cập nhật tài khoản', 500);
            }
        } catch (Exception $e) {
            $this->sendError('Lỗi khi cập nhật tài khoản: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Delete account (admin only)
     */
    private function deleteAccount($id)
    {
        // Check admin permission
        $this->requireAdmin();

        // Check if account exists
        $existingAccount = $this->getAccountById($id);
        if (!$existingAccount) {
            $this->sendError('Không tìm thấy tài khoản', 404);
        }

        // Prevent deleting own account
        $currentUser = $this->requireAuth();
        if ($currentUser['id'] == $id) {
            $this->sendError('Không thể xóa tài khoản của chính mình', 400);
        }

        try {
            $query = "DELETE FROM account WHERE id = ?";
            $stmt = $this->db->prepare($query);
            $result = $stmt->execute([$id]);

            if ($result) {
                $this->sendSuccess([], 'Xóa tài khoản thành công');
            } else {
                $this->sendError('Lỗi khi xóa tài khoản', 500);
            }
        } catch (Exception $e) {
            $this->sendError('Lỗi khi xóa tài khoản: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get account by ID (helper method)
     */
    private function getAccountById($id)
    {
        try {
            $query = "SELECT id, username, fullname, role, created_at FROM account WHERE id = ?";
            $stmt = $this->db->prepare($query);
            $stmt->execute([$id]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * Refresh token
     */
    private function refreshToken()
    {
        $user = $this->requireAuth();

        try {
            $account = $this->accountModel->getAccountByUsername($user['username']);

            if (!$account) {
                $this->sendError('Không tìm thấy tài khoản', 404);
            }

            unset($account['password']);
            $token = $this->generateToken($account);

            $response = [
                'user' => $account,
                'token' => $token,
                'expires_in' => JWT_EXPIRATION
            ];

            $this->sendSuccess($response, 'Làm mới token thành công');
        } catch (Exception $e) {
            $this->sendError('Lỗi khi làm mới token: ' . $e->getMessage(), 500);
        }
    }
}
