<?php include 'app/views/shares/header.php'; ?>

<!-- Flash Messages -->
<?php if (isset($_SESSION['flash'])): ?>
    <div class="alert alert-<?php echo $_SESSION['flash']['type'] === 'error' ? 'danger' : 'success'; ?> alert-dismissible fade show" role="alert">
        <?php echo $_SESSION['flash']['message']; ?>
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <?php unset($_SESSION['flash']); ?>
<?php endif; ?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/shopbanhang/Product/">Trang chủ</a></li>
        <li class="breadcrumb-item active" aria-current="page">Thông tin tài khoản</li>
    </ol>
</nav>

<div class="container">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h3 class="mb-0">
                        <i class="fas fa-user-circle mr-2"></i>
                        Thông tin tài khoản
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 text-center">
                            <div class="profile-avatar mb-3">
                                <i class="fas fa-user-circle fa-5x text-primary"></i>
                            </div>
                            <h5><?php echo htmlspecialchars($user['fullname']); ?></h5>
                            <p class="text-muted">@<?php echo htmlspecialchars($user['username']); ?></p>
                            <?php if ($user['role'] === 'admin'): ?>
                                <span class="badge badge-warning">Administrator</span>
                            <?php else: ?>
                                <span class="badge badge-info">User</span>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-8">
                            <h5>Thông tin chi tiết</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>ID:</strong></td>
                                    <td><?php echo htmlspecialchars($user['id']); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Username:</strong></td>
                                    <td><?php echo htmlspecialchars($user['username']); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Họ và tên:</strong></td>
                                    <td><?php echo htmlspecialchars($user['fullname']); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Vai trò:</strong></td>
                                    <td>
                                        <?php if ($user['role'] === 'admin'): ?>
                                            <span class="badge badge-warning">Administrator</span>
                                        <?php else: ?>
                                            <span class="badge badge-info">User</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="row">
                        <div class="col-md-6">
                            <a href="/shopbanhang/Product/" class="btn btn-secondary">
                                <i class="fas fa-arrow-left mr-2"></i>Về trang chủ
                            </a>
                        </div>
                        <div class="col-md-6 text-right">
                            <a href="/shopbanhang/Account/logout" class="btn btn-danger"
                               onclick="return confirm('Bạn có chắc muốn đăng xuất?')">
                                <i class="fas fa-sign-out-alt mr-2"></i>Đăng xuất
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt mr-2"></i>
                        Thao tác nhanh
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-2">
                            <a href="/shopbanhang/Product/" class="btn btn-outline-primary btn-block">
                                <i class="fas fa-shopping-bag mr-2"></i>Mua sắm
                            </a>
                        </div>
                        <div class="col-md-6 mb-2">
                            <a href="/shopbanhang/Product/cart" class="btn btn-outline-success btn-block">
                                <i class="fas fa-shopping-cart mr-2"></i>Xem giỏ hàng
                            </a>
                        </div>
                        <?php if ($user['role'] === 'admin'): ?>
                            <div class="col-md-6 mb-2">
                                <a href="/shopbanhang/Product/add" class="btn btn-outline-warning btn-block">
                                    <i class="fas fa-plus-circle mr-2"></i>Thêm sản phẩm
                                </a>
                            </div>
                            <div class="col-md-6 mb-2">
                                <a href="/shopbanhang/Account/manage" class="btn btn-outline-info btn-block">
                                    <i class="fas fa-users mr-2"></i>Quản lý tài khoản
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.profile-avatar {
    margin-bottom: 1rem;
}

.table-borderless td {
    border: none;
    padding: 0.5rem 0;
}

.card-footer .row {
    align-items: center;
}
</style>

<?php include 'app/views/shares/footer.php'; ?>
