# RESTful API Documentation

## Base URL
```
http://localhost:8080/api/v1
```

## Authentication
Sử dụng Bearer Token trong header:
```
Authorization: Bearer {token}
```

## Response Format
Tất cả API responses đều có format JSON:

### Success Response
```json
{
    "success": true,
    "message": "Success message",
    "status_code": 200,
    "data": {}
}
```

### Error Response
```json
{
    "success": false,
    "message": "Error message",
    "status_code": 400,
    "errors": {}
}
```

## API Endpoints

### 1. Authentication APIs

#### 1.1 Login
- **URL:** `POST /api/v1/auth/login`
- **Body:**
```json
{
    "username": "admin",
    "password": "password"
}
```
- **Response:**
```json
{
    "success": true,
    "message": "Đăng nhập thành công",
    "data": {
        "user": {
            "id": 1,
            "username": "admin",
            "fullname": "Administrator",
            "role": "admin"
        },
        "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "expires_in": 3600
    }
}
```

#### 1.2 Register
- **URL:** `POST /api/v1/auth/register`
- **Body:**
```json
{
    "username": "newuser",
    "fullname": "New User",
    "password": "password123"
}
```

#### 1.3 Get Profile
- **URL:** `GET /api/v1/auth/profile`
- **Headers:** `Authorization: Bearer {token}`

#### 1.4 Update Profile
- **URL:** `PUT /api/v1/auth/profile`
- **Headers:** `Authorization: Bearer {token}`
- **Body:**
```json
{
    "fullname": "Updated Name"
}
```

#### 1.5 Change Password
- **URL:** `PUT /api/v1/auth/password`
- **Headers:** `Authorization: Bearer {token}`
- **Body:**
```json
{
    "current_password": "oldpassword",
    "new_password": "newpassword"
}
```

### 2. Product APIs

#### 2.1 Get All Products
- **URL:** `GET /api/v1/products`
- **Query Parameters:**
  - `page` (optional): Page number (default: 1)
  - `limit` (optional): Items per page (default: 10, max: 100)
  - `category_id` (optional): Filter by category ID
  - `search` (optional): Search in name and description

#### 2.2 Get Single Product
- **URL:** `GET /api/v1/products/{id}`

#### 2.3 Create Product (Admin only)
- **URL:** `POST /api/v1/products`
- **Headers:** `Authorization: Bearer {token}`
- **Body (multipart/form-data):**
```
name: "Product Name"
description: "Product Description"
price: 100000
category_id: 1
image: [file upload]
```

#### 2.4 Update Product (Admin only)
- **URL:** `PUT /api/v1/products/{id}`
- **Headers:** `Authorization: Bearer {token}`
- **Body:**
```json
{
    "name": "Updated Product Name",
    "description": "Updated Description",
    "price": 150000,
    "category_id": 1
}
```

#### 2.5 Delete Product (Admin only)
- **URL:** `DELETE /api/v1/products/{id}`
- **Headers:** `Authorization: Bearer {token}`

### 3. Category APIs

#### 3.1 Get All Categories
- **URL:** `GET /api/v1/categories`

#### 3.2 Get Single Category
- **URL:** `GET /api/v1/categories/{id}`

#### 3.3 Get Products by Category
- **URL:** `GET /api/v1/categories/{id}/products`
- **Query Parameters:**
  - `page` (optional): Page number
  - `limit` (optional): Items per page

#### 3.4 Create Category (Admin only)
- **URL:** `POST /api/v1/categories`
- **Headers:** `Authorization: Bearer {token}`
- **Body:**
```json
{
    "name": "Category Name",
    "description": "Category Description"
}
```

#### 3.5 Update Category (Admin only)
- **URL:** `PUT /api/v1/categories/{id}`
- **Headers:** `Authorization: Bearer {token}`
- **Body:**
```json
{
    "name": "Updated Category Name",
    "description": "Updated Description"
}
```

#### 3.6 Delete Category (Admin only)
- **URL:** `DELETE /api/v1/categories/{id}`
- **Headers:** `Authorization: Bearer {token}`

### 4. Order APIs

#### 4.1 Get All Orders (Admin only)
- **URL:** `GET /api/v1/orders`
- **Headers:** `Authorization: Bearer {token}`
- **Query Parameters:**
  - `page` (optional): Page number
  - `limit` (optional): Items per page

#### 4.2 Get Single Order
- **URL:** `GET /api/v1/orders/{id}`

#### 4.3 Create Order
- **URL:** `POST /api/v1/orders`
- **Body:**
```json
{
    "customer_name": "John Doe",
    "customer_phone": "0123456789",
    "customer_address": "123 Main St, City",
    "items": [
        {
            "product_id": 1,
            "quantity": 2
        },
        {
            "product_id": 2,
            "quantity": 1
        }
    ]
}
```

#### 4.4 Search Orders by Phone
- **URL:** `POST /api/v1/orders/search`
- **Body:**
```json
{
    "phone": "0123456789"
}
```

#### 4.5 Get My Orders (Authenticated user)
- **URL:** `GET /api/v1/orders/my-orders`
- **Headers:** `Authorization: Bearer {token}`

#### 4.6 Update Order Status (Admin only)
- **URL:** `PUT /api/v1/orders/{id}`
- **Headers:** `Authorization: Bearer {token}`
- **Body:**
```json
{
    "status": "processing"
}
```
- **Valid statuses:** `pending`, `processing`, `shipped`, `delivered`, `cancelled`

#### 4.7 Delete Order (Admin only)
- **URL:** `DELETE /api/v1/orders/{id}`
- **Headers:** `Authorization: Bearer {token}`

### 5. Account Management APIs (Admin only)

#### 5.1 Get All Accounts
- **URL:** `GET /api/v1/accounts`
- **Headers:** `Authorization: Bearer {token}`

#### 5.2 Get Single Account
- **URL:** `GET /api/v1/accounts/{id}`
- **Headers:** `Authorization: Bearer {token}`

#### 5.3 Create Account
- **URL:** `POST /api/v1/accounts`
- **Headers:** `Authorization: Bearer {token}`
- **Body:**
```json
{
    "username": "newuser",
    "fullname": "New User",
    "password": "password123",
    "role": "user"
}
```

## HTTP Status Codes

- `200` - OK
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `405` - Method Not Allowed
- `409` - Conflict
- `500` - Internal Server Error

## Error Handling

API sẽ trả về error messages bằng tiếng Việt và có thể bao gồm chi tiết lỗi trong trường `errors`.

Example:
```json
{
    "success": false,
    "message": "Dữ liệu không hợp lệ",
    "status_code": 400,
    "errors": {
        "name": "Tên sản phẩm không được để trống",
        "price": "Giá sản phẩm không hợp lệ"
    }
}
```
