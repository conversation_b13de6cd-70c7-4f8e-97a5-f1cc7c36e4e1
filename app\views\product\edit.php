<?php include 'app/views/shares/header.php'; ?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="mb-4">
    <ol class="breadcrumb bg-light py-2 px-3 rounded shadow-sm">
        <li class="breadcrumb-item"><a href="/shopbanhang/Product/"><i class="fas fa-home"></i> Trang chủ</a></li>
        <li class="breadcrumb-item"><a href="/shopbanhang/Product/show/<?php echo $product->id; ?>">Chi tiết sản phẩm</a></li>
        <li class="breadcrumb-item active" aria-current="page">Sửa sản phẩm</li>
    </ol>
</nav>

<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card shadow-lg border-0 mb-5">
            <div class="card-header text-center">
                <h2 class="mb-0"><i class="fas fa-edit mr-2"></i>Sửa sản phẩm</h2>
            </div>
            <div class="card-body p-4 p-lg-5">
                <?php if (!empty($errors)): ?>
                    <div class="alert alert-danger">
                        <h5 class="alert-heading"><i class="fas fa-exclamation-triangle mr-2"></i>Lỗi!</h5>
                        <ul class="mb-0">
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo htmlspecialchars($error, ENT_QUOTES, 'UTF-8'); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <form method="POST" action="/shopbanhang/Product/update" enctype="multipart/form-data" onsubmit="return validateForm();">
                    <input type="hidden" name="id" value="<?php echo $product->id; ?>">

                    <div class="form-group">
                        <label for="name"><i class="fas fa-tag mr-2"></i>Tên sản phẩm:</label>
                        <input type="text" id="name" name="name" class="form-control"
                               value="<?php echo htmlspecialchars($product->name, ENT_QUOTES, 'UTF-8'); ?>" required>
                    </div>

                    <div class="form-group">
                        <label for="description"><i class="fas fa-align-left mr-2"></i>Mô tả:</label>
                        <textarea id="description" name="description" class="form-control" rows="5" required><?php echo htmlspecialchars($product->description, ENT_QUOTES, 'UTF-8'); ?></textarea>
                    </div>

                    <div class="form-group">
                        <label for="price"><i class="fas fa-money-bill-wave mr-2"></i>Giá:</label>
                        <div class="input-group">
                            <input type="number" id="price" name="price" class="form-control" step="0.01"
                                   value="<?php echo htmlspecialchars($product->price, ENT_QUOTES, 'UTF-8'); ?>" required>
                            <div class="input-group-append">
                                <span class="input-group-text">VND</span>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="category_id"><i class="fas fa-folder mr-2"></i>Danh mục:</label>
                        <select id="category_id" name="category_id" class="form-control" required>
                            <?php foreach ($categories as $category): ?>
                                <option value="<?php echo $category->id; ?>" <?php echo $category->id == $product->category_id ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($category->name, ENT_QUOTES, 'UTF-8'); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="image"><i class="fas fa-image mr-2"></i>Hình ảnh:</label>

                        <?php if ($product->image): ?>
                            <div class="mb-3">
                                <p class="mb-2">Hình ảnh hiện tại:</p>
                                <div class="d-flex align-items-center p-3 bg-light rounded">
                                    <img src="/shopbanhang/<?php echo $product->image; ?>" alt="Product Image" class="img-thumbnail mr-3" style="max-width: 100px;">
                                    <div>
                                        <p class="mb-0 text-muted"><?php echo basename($product->image); ?></p>
                                        <small class="text-muted">Tải lên hình ảnh mới để thay thế</small>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>

                        <div class="custom-file">
                            <input type="file" class="custom-file-input" id="image" name="image">
                            <label class="custom-file-label" for="image">Chọn hình ảnh mới</label>
                        </div>
                        <input type="hidden" name="existing_image" value="<?php echo $product->image; ?>">
                        <small class="form-text text-muted">Hỗ trợ các định dạng: JPG, JPEG, PNG, GIF và WEBP</small>
                    </div>

                    <div class="form-group mt-5 text-center">
                        <button type="submit" class="btn btn-primary btn-lg px-5">
                            <i class="fas fa-save mr-2"></i>Lưu thay đổi
                        </button>
                        <a href="/shopbanhang/Product/show/<?php echo $product->id; ?>" class="btn btn-secondary btn-lg px-5 ml-2">
                            <i class="fas fa-arrow-left mr-2"></i>Quay lại
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for file input -->
<script>
    // Show file name when selected
    document.querySelector('.custom-file-input').addEventListener('change', function(e) {
        if (e.target.files.length > 0) {
            var fileName = e.target.files[0].name;
            var nextSibling = e.target.nextElementSibling;
            nextSibling.innerText = fileName;
        }
    });
</script>

<?php include 'app/views/shares/footer.php'; ?>