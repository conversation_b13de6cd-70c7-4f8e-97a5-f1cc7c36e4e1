<?php include 'app/views/shares/header.php'; ?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/shopbanhang/Product/">Trang chủ</a></li>
        <li class="breadcrumb-item active" aria-current="page">Quản lý đơn hàng</li>
    </ol>
</nav>

<div class="card">
    <div class="card-header">
        <h4 class="mb-0"><i class="fas fa-shopping-bag mr-2"></i>Quản lý đơn hàng</h4>
    </div>
    <div class="card-body">
        <div class="table-responsive">            <table class="table table-hover">
                <thead class="thead-light">
                    <tr>
                        <th class="text-center" style="width: 80px">Mã đơn</th>
                        <th style="width: 200px"><PERSON><PERSON><PERSON><PERSON> hàng</th>
                        <th style="width: 120px"><PERSON>ố điện thoại</th>
                        <th class="text-right" style="width: 150px">Tổng tiền</th>
                        <th style="width: 150px">Ngày đặt</th>
                        <th class="text-center" style="width: 150px">Trạng thái</th>
                        <th class="text-center" style="width: 100px">Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($orders['orders'] as $order): ?>
                        <tr>
                            <td class="text-center"><strong>#<?php echo $order['id']; ?></strong></td>
                            <td><?php echo htmlspecialchars($order['name']); ?></td>
                            <td><?php echo htmlspecialchars($order['phone']); ?></td>
                            <td class="text-right text-success font-weight-bold"><?php echo number_format($order['total_amount'], 0, ',', '.'); ?>đ</td>
                            <td><?php echo date('d/m/Y H:i', strtotime($order['created_at'])); ?></td><td class="text-center">
                                <?php
                                    $statusClasses = [
                                        'pending' => 'badge-warning',
                                        'processing' => 'badge-info',
                                        'shipping' => 'badge-primary',
                                        'completed' => 'badge-success',
                                        'cancelled' => 'badge-danger'
                                    ];
                                    $statusLabels = [
                                        'pending' => 'Chờ xử lý',
                                        'processing' => 'Đang xử lý',
                                        'shipping' => 'Đang giao hàng',
                                        'completed' => 'Đã hoàn thành',
                                        'cancelled' => 'Đã hủy'
                                    ];
                                ?>
                                <span class="badge <?php echo $statusClasses[$order['status']] ?? 'badge-secondary'; ?>">
                                    <?php echo $statusLabels[$order['status']] ?? 'Không xác định'; ?>
                                </span>
                            </td>
                            <td class="text-center">
                                <div class="btn-group btn-group-sm">
                                    <a href="/shopbanhang/Order/detail/<?php echo $order['id']; ?>" 
                                       class="btn btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <!-- Phân trang -->
        <?php if ($orders['total_pages'] > 1): ?>
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination justify-content-center">
                    <?php for ($i = 1; $i <= $orders['total_pages']; $i++): ?>
                        <li class="page-item <?php echo $i === $orders['page'] ? 'active' : ''; ?>">
                            <a class="page-link" href="/shopbanhang/Order/manage?page=<?php echo $i; ?>">
                                <?php echo $i; ?>
                            </a>
                        </li>
                    <?php endfor; ?>
                </ul>
            </nav>
        <?php endif; ?>
    </div>
</div>

<style>
.status-select {
    font-size: 14px;
    padding: 4px 8px;
    height: auto;
}
</style>

<?php include 'app/views/shares/footer.php'; ?>
