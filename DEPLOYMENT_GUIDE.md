# Hướng Dẫn Triển <PERSON>hai RESTful API

## 1. <PERSON><PERSON><PERSON> Bị Môi Trường

### 1.1 <PERSON><PERSON><PERSON>ống
- **PHP:** 7.4 hoặc cao hơn
- **MySQL:** 5.7 hoặc cao hơn
- **Web Server:** Apache với mod_rewrite hoặc Nginx
- **Extensions:** PDO, cURL, JSON, GD (cho upload ảnh)

### 1.2 Kiểm Tra PHP Extensions
```bash
php -m | grep -E "(pdo|curl|json|gd)"
```

## 2. Cài Đặt Database

### 2.1 Tạo Database
```sql
CREATE DATABASE my_store CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 2.2 Import Schema
```bash
mysql -u root -p my_store < database.sql
```

### 2.3 Ki<PERSON>m <PERSON>ra Tables
```sql
USE my_store;
SHOW TABLES;
-- <PERSON><PERSON><PERSON> quả mong đợi: account, category, product, orders, order_details
```

## 3. C<PERSON>u Hình Ứng Dụng

### 3.1 Cấu <PERSON> Database
Chỉnh sửa file `app/config/config.php`:

```php
// Database configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'my_store');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
define('DB_PORT', '3306');

// URL Configuration
define('BASE_URL', 'http://your-domain.com');
```

### 3.2 Cấu Hình JWT Secret
Thay đổi JWT secret trong config:
```php
define('JWT_SECRET', 'your-very-secure-secret-key-here');
```

### 3.3 Cấu Hình Upload Directory
Đảm bảo thư mục uploads có quyền ghi:
```bash
chmod 755 uploads/
chown www-data:www-data uploads/
```

## 4. Cấu Hình Web Server

### 4.1 Apache Configuration
Đảm bảo mod_rewrite được enable:
```bash
sudo a2enmod rewrite
sudo systemctl restart apache2
```

Virtual Host configuration:
```apache
<VirtualHost *:80>
    ServerName your-domain.com
    DocumentRoot /path/to/SHOPBANHANGAPI
    
    <Directory /path/to/SHOPBANHANGAPI>
        AllowOverride All
        Require all granted
    </Directory>
    
    ErrorLog ${APACHE_LOG_DIR}/shopbanhang_error.log
    CustomLog ${APACHE_LOG_DIR}/shopbanhang_access.log combined
</VirtualHost>
```

### 4.2 Nginx Configuration
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/SHOPBANHANGAPI;
    index index.php;

    location / {
        try_files $uri $uri/ /index.php?url=$uri&$args;
    }

    location ~ ^/api/ {
        try_files $uri $uri/ /api.php?$args;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\. {
        deny all;
    }
}
```

## 5. Kiểm Tra Cài Đặt

### 5.1 Test Web Interface
Truy cập: `http://your-domain.com`
- Kiểm tra trang chủ hiển thị sản phẩm
- Test đăng nhập admin

### 5.2 Test API Endpoints
```bash
# Test API health
curl -X GET http://your-domain.com/api/v1/categories

# Test login
curl -X POST http://your-domain.com/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'
```

### 5.3 Chạy Test Script
```bash
php test_api.php
```

## 6. Security Hardening

### 6.1 File Permissions
```bash
# Set proper permissions
find . -type f -exec chmod 644 {} \;
find . -type d -exec chmod 755 {} \;
chmod 755 uploads/

# Protect sensitive files
chmod 600 app/config/config.php
```

### 6.2 Hide Sensitive Information
Tạo file `.env` cho production:
```bash
DB_HOST=localhost
DB_NAME=my_store
DB_USER=production_user
DB_PASS=secure_password
JWT_SECRET=very-secure-jwt-secret-key
```

### 6.3 SSL Configuration
Cài đặt SSL certificate:
```bash
# Using Let's Encrypt
sudo certbot --apache -d your-domain.com
```

## 7. Performance Optimization

### 7.1 PHP Configuration
Chỉnh sửa `php.ini`:
```ini
memory_limit = 256M
upload_max_filesize = 10M
post_max_size = 10M
max_execution_time = 30
opcache.enable = 1
```

### 7.2 Database Optimization
```sql
-- Add indexes for better performance
CREATE INDEX idx_product_category ON product(category_id);
CREATE INDEX idx_order_phone ON orders(phone);
CREATE INDEX idx_order_user ON orders(user_id);
CREATE INDEX idx_order_details_order ON order_details(order_id);
```

### 7.3 Caching (Optional)
Implement Redis caching:
```php
// In BaseApiController.php
protected function getCachedData($key, $callback, $ttl = 3600) {
    if (class_exists('Redis')) {
        $redis = new Redis();
        $redis->connect('127.0.0.1', 6379);
        
        $cached = $redis->get($key);
        if ($cached) {
            return json_decode($cached, true);
        }
        
        $data = $callback();
        $redis->setex($key, $ttl, json_encode($data));
        return $data;
    }
    
    return $callback();
}
```

## 8. Monitoring và Logging

### 8.1 Error Logging
Tạo file `app/config/logger.php`:
```php
<?php
class Logger {
    public static function log($message, $level = 'INFO') {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[$timestamp] [$level] $message" . PHP_EOL;
        file_put_contents(SITE_ROOT . '/logs/api.log', $logMessage, FILE_APPEND);
    }
}
```

### 8.2 API Analytics
Track API usage:
```php
// In BaseApiController.php
protected function logApiCall() {
    $data = [
        'timestamp' => date('Y-m-d H:i:s'),
        'method' => $this->requestMethod,
        'endpoint' => $_SERVER['REQUEST_URI'],
        'ip' => $_SERVER['REMOTE_ADDR'],
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
    ];
    
    Logger::log(json_encode($data), 'API_CALL');
}
```

## 9. Backup Strategy

### 9.1 Database Backup
Tạo script backup tự động:
```bash
#!/bin/bash
# backup_db.sh
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -u username -p password my_store > backups/db_backup_$DATE.sql
find backups/ -name "*.sql" -mtime +7 -delete
```

### 9.2 File Backup
```bash
#!/bin/bash
# backup_files.sh
DATE=$(date +%Y%m%d_%H%M%S)
tar -czf backups/files_backup_$DATE.tar.gz uploads/ app/config/
find backups/ -name "*.tar.gz" -mtime +7 -delete
```

## 10. Troubleshooting

### 10.1 Common Issues

**API returns 404:**
- Kiểm tra .htaccess có hoạt động
- Kiểm tra mod_rewrite enabled
- Kiểm tra file api.php tồn tại

**Database connection failed:**
- Kiểm tra credentials trong config
- Kiểm tra MySQL service running
- Kiểm tra firewall settings

**File upload fails:**
- Kiểm tra permissions của uploads folder
- Kiểm tra PHP upload settings
- Kiểm tra disk space

**CORS errors:**
- Kiểm tra CORS headers trong .htaccess
- Kiểm tra Origin domain
- Test với Postman để loại trừ browser issues

### 10.2 Debug Mode
Enable debug mode trong development:
```php
// In config.php
define('DEBUG_MODE', true);

// In BaseApiController.php
if (defined('DEBUG_MODE') && DEBUG_MODE) {
    ini_set('display_errors', 1);
    error_reporting(E_ALL);
}
```

## 11. Production Checklist

- [ ] Database credentials secured
- [ ] JWT secret changed
- [ ] SSL certificate installed
- [ ] File permissions set correctly
- [ ] Error logging configured
- [ ] Backup strategy implemented
- [ ] Performance optimizations applied
- [ ] Security headers configured
- [ ] API documentation updated
- [ ] Monitoring tools setup

## 12. Maintenance

### 12.1 Regular Tasks
- Monitor API logs
- Check database performance
- Update dependencies
- Review security logs
- Test backup restoration

### 12.2 Updates
Khi cập nhật code:
1. Backup database và files
2. Test trên staging environment
3. Deploy với downtime tối thiểu
4. Verify API functionality
5. Monitor for errors

## Support

Nếu gặp vấn đề trong quá trình triển khai:
1. Kiểm tra logs: `/logs/api.log`
2. Kiểm tra Apache/Nginx error logs
3. Test với script: `php test_api.php`
4. Verify database connectivity
5. Check file permissions
