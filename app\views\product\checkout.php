<?php include 'app/views/shares/header.php'; ?>

<!-- Flash Messages -->
<?php if (isset($_SESSION['flash'])): ?>
    <div class="alert alert-<?php echo $_SESSION['flash']['type'] === 'error' ? 'danger' : 'success'; ?> alert-dismissible fade show" role="alert">
        <?php echo $_SESSION['flash']['message']; ?>
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <?php unset($_SESSION['flash']); ?>
<?php endif; ?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/shopbanhang/Product/">Trang chủ</a></li>
        <li class="breadcrumb-item"><a href="/shopbanhang/Product/cart">Giỏ hàng</a></li>
        <li class="breadcrumb-item active" aria-current="page">Thanh toán</li>
    </ol>
</nav>

<div class="row">
    <!-- Form thông tin khách hàng -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-user mr-2"></i>
                    Thông tin khách hàng
                </h4>
            </div>
            <div class="card-body">
                <form method="POST" action="/shopbanhang/Product/processCheckout" id="checkoutForm" class="checkout-form">
                    <div class="form-group">
                        <label for="customer_name">Họ và tên <span class="text-danger">*</span></label>
                        <input type="text"
                               class="form-control"
                               id="customer_name"
                               name="customer_name"
                               required
                               minlength="2"
                               placeholder="Nhập họ và tên của bạn">
                        <div class="invalid-feedback">
                            Vui lòng nhập họ và tên (ít nhất 2 ký tự).
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="customer_phone">Số điện thoại <span class="text-danger">*</span></label>
                        <input type="tel"
                               class="form-control"
                               id="customer_phone"
                               name="customer_phone"
                               required
                               pattern="[0-9]{10,11}"
                               placeholder="Nhập số điện thoại (10-11 số)">
                        <div class="invalid-feedback">
                            Vui lòng nhập số điện thoại hợp lệ (10-11 số).
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="customer_address">Địa chỉ giao hàng <span class="text-danger">*</span></label>
                        <textarea class="form-control"
                                  id="customer_address"
                                  name="customer_address"
                                  rows="3"
                                  required
                                  minlength="5"
                                  placeholder="Nhập địa chỉ giao hàng chi tiết (ít nhất 5 ký tự)"></textarea>
                        <div class="invalid-feedback">
                            Vui lòng nhập địa chỉ giao hàng (ít nhất 5 ký tự).
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="agree_terms" required>
                            <label class="form-check-label" for="agree_terms">
                                Tôi đồng ý với <a href="/shopbanhang/Product/terms" target="_blank">điều khoản và điều kiện</a> của cửa hàng <span class="text-danger">*</span>
                            </label>
                            <div class="invalid-feedback">
                                Bạn phải đồng ý với điều khoản và điều kiện.
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <a href="/shopbanhang/Product/cart" class="btn btn-secondary btn-block">
                                <i class="fas fa-arrow-left mr-2"></i>Quay lại giỏ hàng
                            </a>
                        </div>
                        <div class="col-md-6">
                            <button type="submit" class="btn btn-success btn-block">
                                <i class="fas fa-check mr-2"></i>Xác nhận đặt hàng
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Tóm tắt đơn hàng -->
    <div class="col-md-4">
        <div class="card border-primary">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-receipt mr-2"></i>
                    Tóm tắt đơn hàng
                </h5>
            </div>
            <div class="card-body">
                <?php foreach ($cartItems as $productId => $item): ?>
                    <div class="order-summary-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="flex-grow-1">
                                <h6 class="mb-0 text-truncate" style="max-width: 150px;" title="<?php echo htmlspecialchars($item['name']); ?>">
                                    <?php echo htmlspecialchars($item['name']); ?>
                                </h6>
                                <small class="text-muted">
                                    <?php echo number_format($item['price'], 0, ',', '.'); ?>đ × <?php echo $item['quantity']; ?>
                                </small>
                            </div>
                            <div class="text-right">
                                <span class="font-weight-bold text-primary">
                                    <?php echo number_format($item['price'] * $item['quantity'], 0, ',', '.'); ?>đ
                                </span>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>

                <hr>

                <div class="d-flex justify-content-between mb-2">
                    <span>Số lượng sản phẩm:</span>
                    <span class="font-weight-bold"><?php echo count($cartItems); ?></span>
                </div>

                <div class="d-flex justify-content-between mb-2">
                    <span>Tổng số lượng:</span>
                    <span class="font-weight-bold">
                        <?php
                        $totalQuantity = 0;
                        foreach ($cartItems as $item) {
                            $totalQuantity += $item['quantity'];
                        }
                        echo $totalQuantity;
                        ?>
                    </span>
                </div>

                <div class="d-flex justify-content-between mb-2">
                    <span>Phí vận chuyển:</span>
                    <span class="text-success">Miễn phí</span>
                </div>

                <hr>

                <div class="d-flex justify-content-between">
                    <h5>Tổng cộng:</h5>
                    <h5 class="text-danger font-weight-bold">
                        <?php echo number_format($totalAmount, 0, ',', '.'); ?>đ
                    </h5>
                </div>
            </div>
        </div>

        <!-- Thông tin bảo mật -->
        <div class="card mt-3">
            <div class="card-body text-center">
                <i class="fas fa-shield-alt fa-2x text-success mb-2"></i>
                <h6>Thanh toán an toàn</h6>
                <small class="text-muted">
                    Thông tin của bạn được bảo mật và mã hóa
                </small>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for form validation -->
<script>
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Add Bootstrap validation classes
document.getElementById('checkoutForm').classList.add('needs-validation');
</script>

<?php include 'app/views/shares/footer.php'; ?>
