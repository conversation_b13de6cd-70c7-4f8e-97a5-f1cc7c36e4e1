# Quick Fix Guide - Sửa Lỗi API Nhanh

## 🚀 Các <PERSON>ớc Sửa Lỗi Đã Thực Hiện

### 1. **Sửa lỗi Authentication Token**
**Vấn đề:** Token validation không hoạt động đúng, `getallheaders()` không tương thích với tất cả server.

**Đã sửa:**
- Thay thế `getallheaders()` bằng method tương thích đa nền tảng
- Cải thiện token validation và decoding
- Thêm proper error handling

### 2. **Thêm Missing Methods**
**Vấn đề:** Thiếu methods `updateAccount()` và `deleteAccount()` trong ApiAccountController.

**Đã sửa:**
- Thêm method `updateAccount()` cho admin
- Thêm method `deleteAccount()` cho admin
- Thêm method helper `getAccountById()`

### 3. **<PERSON><PERSON><PERSON> thiện Error <PERSON>ling**
**Vấn đề:** Một số lỗi không được xử lý đúng cách.

**Đ<PERSON> sửa:**
- Thêm try-catch blocks
- Cải thiện error messages
- Thêm validation cho edge cases

## 🛠️ Cách Kiểm Tra và Sửa Lỗi

### Bước 1: Kiểm tra Setup
```bash
php check_api_setup.php
```

### Bước 2: Tạo Admin Account
```bash
php create_admin.php
```

### Bước 3: Test API
```bash
php test_api.php
```

## 🔧 Các Lỗi Thường Gặp và Cách Sửa

### Lỗi 1: "Token xác thực không hợp lệ"
**Nguyên nhân:** 
- Token không được gửi đúng format
- Token đã hết hạn
- Server không đọc được Authorization header

**Cách sửa:**
1. Kiểm tra format header: `Authorization: Bearer {token}`
2. Đảm bảo token chưa hết hạn (1 giờ)
3. Login lại để lấy token mới

**Test với cURL:**
```bash
# Login để lấy token
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'

# Sử dụng token
curl -X GET http://localhost:8080/api/v1/auth/profile \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### Lỗi 2: "Database connection failed"
**Nguyên nhân:** Cấu hình database không đúng

**Cách sửa:**
1. Kiểm tra `app/config/config.php`
2. Đảm bảo MySQL service đang chạy
3. Kiểm tra credentials

```php
// app/config/config.php
define('DB_HOST', 'localhost');
define('DB_NAME', 'my_store');
define('DB_USER', 'root');
define('DB_PASS', '');
```

### Lỗi 3: "404 Not Found" cho API endpoints
**Nguyên nhân:** URL rewriting không hoạt động

**Cách sửa:**
1. Kiểm tra `.htaccess` file tồn tại
2. Đảm bảo mod_rewrite enabled (Apache)
3. Kiểm tra file `api.php` tồn tại

**Apache:**
```bash
sudo a2enmod rewrite
sudo systemctl restart apache2
```

### Lỗi 4: "CORS Error" trong browser
**Nguyên nhân:** CORS headers không được set đúng

**Cách sửa:**
1. Kiểm tra `.htaccess` có CORS rules
2. Test với Postman thay vì browser
3. Đảm bảo server trả về đúng headers

### Lỗi 5: File upload không hoạt động
**Nguyên nhân:** 
- Thư mục uploads không có quyền ghi
- PHP upload settings

**Cách sửa:**
```bash
# Set permissions
chmod 755 uploads/
chown www-data:www-data uploads/

# Check PHP settings
php -i | grep upload
```

## 📋 Checklist Troubleshooting

### Server Setup
- [ ] PHP 7.4+ installed
- [ ] MySQL running
- [ ] Apache/Nginx configured
- [ ] mod_rewrite enabled (Apache)

### Files
- [ ] All API files exist
- [ ] .htaccess configured
- [ ] Database imported
- [ ] Admin account created

### Permissions
- [ ] uploads/ directory writable
- [ ] config files readable
- [ ] API files executable

### Database
- [ ] Connection successful
- [ ] All tables exist
- [ ] Admin account exists
- [ ] Sample data loaded

## 🧪 Testing Commands

### Quick API Test
```bash
# Test categories (no auth required)
curl -X GET http://localhost:8080/api/v1/categories

# Test login
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'

# Test products
curl -X GET http://localhost:8080/api/v1/products
```

### Postman Testing
1. Import `SHOPBANHANG_API.postman_collection.json`
2. Set environment variable `base_url` = `http://localhost:8080/api/v1`
3. Run login request first
4. Token will be auto-saved for other requests

## 🆘 Emergency Fixes

### Reset Admin Password
```sql
UPDATE account SET password = '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi' WHERE username = 'admin';
-- Password will be: admin123
```

### Recreate Database
```bash
mysql -u root -p -e "DROP DATABASE IF EXISTS my_store; CREATE DATABASE my_store;"
mysql -u root -p my_store < database.sql
php create_admin.php
```

### Reset File Permissions
```bash
find . -type f -exec chmod 644 {} \;
find . -type d -exec chmod 755 {} \;
chmod 755 uploads/
```

## 📞 Support

Nếu vẫn gặp lỗi:

1. **Chạy diagnostic script:**
   ```bash
   php check_api_setup.php
   ```

2. **Kiểm tra logs:**
   - Apache: `/var/log/apache2/error.log`
   - PHP: Check `error_log` in PHP settings
   - MySQL: `/var/log/mysql/error.log`

3. **Test từng bước:**
   - Database connection
   - File permissions
   - URL rewriting
   - API endpoints

4. **Common solutions:**
   - Restart web server
   - Clear browser cache
   - Check firewall settings
   - Verify port 8080 is open

## ✅ Verification

Sau khi sửa lỗi, verify bằng cách:

1. ✅ `php check_api_setup.php` - All green
2. ✅ `php test_api.php` - All tests pass
3. ✅ Postman collection works
4. ✅ Web interface still works
5. ✅ File uploads work
6. ✅ Authentication works

**API đã sẵn sàng sử dụng! 🎉**
