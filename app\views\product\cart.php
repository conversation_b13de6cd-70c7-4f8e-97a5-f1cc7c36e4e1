<?php include 'app/views/shares/header.php'; ?>

<!-- Flash Messages -->
<?php if (isset($_SESSION['flash'])): ?>
    <div class="alert alert-<?php echo $_SESSION['flash']['type'] === 'error' ? 'danger' : 'success'; ?> alert-dismissible fade show" role="alert">
        <?php echo $_SESSION['flash']['message']; ?>
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <?php unset($_SESSION['flash']); ?>
<?php endif; ?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/shopbanhang/Product/">Trang chủ</a></li>
        <li class="breadcrumb-item active" aria-current="page">Giỏ hàng</li>
    </ol>
</nav>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-shopping-cart mr-2"></i>
                    Giỏ hàng của bạn
                </h4>
            </div>
            <div class="card-body">
                <?php if (empty($cartItems)): ?>
                    <!-- Giỏ hàng trống -->
                    <div class="text-center py-5">
                        <i class="fas fa-shopping-cart fa-5x text-muted mb-3"></i>
                        <h4 class="text-muted">Giỏ hàng của bạn đang trống</h4>
                        <p class="text-muted mb-4">Hãy thêm một số sản phẩm vào giỏ hàng để tiếp tục mua sắm!</p>
                        <a href="/shopbanhang/Product/" class="btn btn-primary">
                            <i class="fas fa-arrow-left mr-2"></i>Tiếp tục mua sắm
                        </a>
                    </div>
                <?php else: ?>
                    <!-- Bảng sản phẩm trong giỏ hàng -->
                    <form method="POST" action="/shopbanhang/Product/updateCart">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="thead-light">
                                    <tr>
                                        <th>Hình ảnh</th>
                                        <th>Tên sản phẩm</th>
                                        <th>Giá</th>
                                        <th>Số lượng</th>
                                        <th>Thành tiền</th>
                                        <th>Thao tác</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($cartItems as $productId => $item): ?>
                                        <tr>
                                            <td>
                                                <img src="/shopbanhang/<?php echo htmlspecialchars($item['image']); ?>"
                                                     alt="<?php echo htmlspecialchars($item['name']); ?>"
                                                     class="img-thumbnail cart-item-image">
                                            </td>
                                            <td>
                                                <h6 class="mb-0"><?php echo htmlspecialchars($item['name']); ?></h6>
                                            </td>
                                            <td>
                                                <span class="text-primary font-weight-bold">
                                                    <?php echo number_format($item['price'], 0, ',', '.'); ?>đ
                                                </span>
                                            </td>
                                            <td>
                                                <div class="input-group cart-quantity-input">
                                                    <input type="number"
                                                           name="quantities[<?php echo $productId; ?>]"
                                                           value="<?php echo $item['quantity']; ?>"
                                                           min="1"
                                                           max="99"
                                                           class="form-control text-center">
                                                </div>
                                            </td>
                                            <td>
                                                <span class="text-success font-weight-bold">
                                                    <?php echo number_format($item['price'] * $item['quantity'], 0, ',', '.'); ?>đ
                                                </span>
                                            </td>
                                            <td>
                                                <a href="/shopbanhang/Product/removeFromCart/<?php echo $productId; ?>"
                                                   class="btn btn-danger btn-sm"
                                                   onclick="return confirm('Bạn có chắc muốn xóa sản phẩm này?')">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Nút cập nhật giỏ hàng -->
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <button type="submit" class="btn btn-secondary">
                                    <i class="fas fa-sync-alt mr-2"></i>Cập nhật giỏ hàng
                                </button>
                                <a href="/shopbanhang/Product/" class="btn btn-outline-primary ml-2">
                                    <i class="fas fa-arrow-left mr-2"></i>Tiếp tục mua sắm
                                </a>
                            </div>
                        </div>
                    </form>

                    <!-- Card tổng kết -->
                    <div class="row mt-4">
                        <div class="col-md-6 ml-auto">
                            <div class="card cart-total-card">
                                <div class="card-header">
                                    <h5 class="mb-0">Tổng kết đơn hàng</h5>
                                </div>
                                <div class="card-body">
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>Số lượng sản phẩm:</span>
                                        <span class="font-weight-bold"><?php echo count($cartItems); ?> sản phẩm</span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-3">
                                        <span>Tổng số lượng:</span>
                                        <span class="font-weight-bold">
                                            <?php
                                            $totalQuantity = 0;
                                            foreach ($cartItems as $item) {
                                                $totalQuantity += $item['quantity'];
                                            }
                                            echo $totalQuantity;
                                            ?> cái
                                        </span>
                                    </div>
                                    <hr>
                                    <div class="d-flex justify-content-between mb-3">
                                        <h5>Tổng tiền:</h5>
                                        <h5 class="text-danger font-weight-bold">
                                            <?php echo number_format($totalAmount, 0, ',', '.'); ?>đ
                                        </h5>
                                    </div>
                                    <a href="/shopbanhang/Product/checkout" class="btn btn-success btn-lg btn-block">
                                        <i class="fas fa-credit-card mr-2"></i>Tiến hành thanh toán
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include 'app/views/shares/footer.php'; ?>
