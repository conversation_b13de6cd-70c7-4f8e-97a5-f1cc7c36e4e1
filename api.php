<?php
require_once 'app/config/bootstrap.php';

// Set content type for API responses
header('Content-Type: application/json; charset=UTF-8');

// Get the request URI and method
$requestUri = $_SERVER['REQUEST_URI'];
$requestMethod = $_SERVER['REQUEST_METHOD'];

// Remove query string from URI
$requestUri = strtok($requestUri, '?');

// Remove base path and api prefix
$basePath = '/api/v1';
if (strpos($requestUri, $basePath) === 0) {
    $requestUri = substr($requestUri, strlen($basePath));
}

// Remove leading slash
$requestUri = ltrim($requestUri, '/');

// Split URI into segments
$uriSegments = explode('/', $requestUri);

// Get controller and action from URI
$controller = isset($uriSegments[0]) ? $uriSegments[0] : '';
$action = isset($uriSegments[1]) ? $uriSegments[1] : null;
$id = isset($uriSegments[2]) ? $uriSegments[2] : null;

// Route mapping
$routes = [
    'products' => 'ApiProductController',
    'categories' => 'ApiCategoryController',
    'orders' => 'ApiOrderController',
    'accounts' => 'ApiAccountController',
    'auth' => 'ApiAccountController'
];

try {
    // Handle special routes
    if ($controller === 'auth') {
        $authController = new ApiAccountController();
        
        switch ($action) {
            case 'login':
                $authController->handleRequest('login');
                break;
            case 'register':
                $authController->handleRequest('register');
                break;
            case 'profile':
                $authController->handleRequest('profile');
                break;
            case 'refresh':
                $authController->handleRequest('refresh');
                break;
            case 'password':
                $authController->handleRequest('password');
                break;
            default:
                http_response_code(404);
                echo json_encode([
                    'success' => false,
                    'message' => 'Endpoint không tồn tại',
                    'status_code' => 404
                ]);
        }
        exit;
    }

    // Handle special category routes
    if ($controller === 'categories' && $action && is_numeric($action) && isset($uriSegments[2]) && $uriSegments[2] === 'products') {
        $categoryController = new ApiCategoryController();
        $categoryController->getProductsByCategory($action);
        exit;
    }

    // Handle special order routes
    if ($controller === 'orders') {
        $orderController = new ApiOrderController();
        
        if ($action === 'search' && $requestMethod === 'POST') {
            $orderController->handleRequest('search');
            exit;
        }
        
        if ($action === 'my-orders' && $requestMethod === 'GET') {
            $orderController->getMyOrders();
            exit;
        }
    }

    // Check if controller exists
    if (!isset($routes[$controller])) {
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'message' => 'API endpoint không tồn tại',
            'status_code' => 404
        ]);
        exit;
    }

    $controllerClass = $routes[$controller];
    $controllerFile = APP_ROOT . '/api/' . $controllerClass . '.php';

    // Check if controller file exists
    if (!file_exists($controllerFile)) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Controller không tồn tại',
            'status_code' => 500
        ]);
        exit;
    }

    // Include controller file and create instance
    require_once $controllerFile;
    $controllerInstance = new $controllerClass();

    // Handle the request
    $controllerInstance->handleRequest($action, $id);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Lỗi server: ' . $e->getMessage(),
        'status_code' => 500
    ], JSON_UNESCAPED_UNICODE);
}
?>
