/* Custom CSS for SHOPBANHANG */

:root {
  --primary-color: #4e73df;
  --secondary-color: #1cc88a;
  --accent-color: #f6c23e;
  --danger-color: #e74a3b;
  --dark-color: #5a5c69;
  --light-color: #f8f9fc;
  --white-color: #ffffff;
  --gray-color: #858796;
  --light-gray: #eaecf4;
}

body {
  font-family: 'Nunito', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: var(--light-color);
  color: var(--dark-color);
}

/* Header Styles */
.navbar {
  box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
  background: linear-gradient(to right, var(--primary-color), #224abe);
  padding: 1rem;
}

.navbar-brand {
  color: var(--white-color) !important;
  font-weight: 700;
  font-size: 1.5rem;
  display: flex;
  align-items: center;
}

.navbar-brand i {
  margin-right: 0.5rem;
  font-size: 1.8rem;
}

.navbar-light .navbar-nav .nav-link {
  color: rgba(255, 255, 255, 0.8) !important;
  font-weight: 600;
  padding: 0.5rem 1rem;
  border-radius: 0.35rem;
  transition: all 0.2s;
}

.navbar-light .navbar-nav .nav-link:hover {
  color: var(--white-color) !important;
  background-color: rgba(255, 255, 255, 0.1);
}

.navbar-light .navbar-toggler {
  border-color: rgba(255, 255, 255, 0.5);
}

.navbar-light .navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 30 30'%3e%3cpath stroke='rgba(255, 255, 255, 0.8)' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

/* Card Styles */
.card {
  border: none;
  border-radius: 0.5rem;
  box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.1);
  transition: transform 0.3s, box-shadow 0.3s;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.15);
}

.card-header {
  background: linear-gradient(to right, var(--primary-color), #224abe);
  color: var(--white-color);
  font-weight: 700;
  border-radius: 0.5rem 0.5rem 0 0 !important;
}

/* Product List Styles */
.product-card {
  margin-bottom: 1.5rem;
  height: 100%;
}

.product-card .card-img-top {
  height: 200px;
  width: 100%;
  object-fit: contain;
  border-radius: 0.5rem 0.5rem 0 0;
  padding: 10px;
  background-color: #f8f9fc;
}

.product-card .card-body {
  padding: 1.5rem;
}

.product-card .card-title {
  font-weight: 700;
  margin-bottom: 0.75rem;
}

.product-card .card-text {
  color: var(--gray-color);
  margin-bottom: 1rem;
}

.product-price {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--primary-color);
}

.product-category {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  background-color: var(--light-gray);
  border-radius: 0.25rem;
  font-size: 0.875rem;
  margin-bottom: 1rem;
}

/* Button Styles */
.btn {
  border-radius: 0.35rem;
  padding: 0.375rem 0.75rem;
  font-weight: 600;
  transition: all 0.2s;
}

.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-primary:hover {
  background-color: #224abe;
  border-color: #224abe;
}

.btn-success {
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
}

.btn-success:hover {
  background-color: #169b6b;
  border-color: #169b6b;
}

.btn-warning {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
  color: #fff;
}

.btn-warning:hover {
  background-color: #dda20a;
  border-color: #dda20a;
  color: #fff;
}

.btn-danger {
  background-color: var(--danger-color);
  border-color: var(--danger-color);
}

.btn-danger:hover {
  background-color: #be2617;
  border-color: #be2617;
}

/* Form Styles */
.form-control {
  border-radius: 0.35rem;
  padding: 0.5rem 1rem;
  border: 1px solid var(--light-gray);
}

.form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

label {
  font-weight: 600;
  color: var(--dark-color);
}

/* Footer Styles */
footer {
  background: linear-gradient(to right, var(--primary-color), #224abe) !important;
  color: var(--white-color) !important;
}

footer h5 {
  font-weight: 700;
  margin-bottom: 1.25rem;
}

footer a {
  color: rgba(255, 255, 255, 0.8) !important;
  transition: color 0.2s;
}

footer a:hover {
  color: var(--white-color) !important;
  text-decoration: none;
}

footer .social-icons a {
  font-size: 1.5rem;
  margin-right: 1rem;
}

footer .copyright {
  background-color: rgba(0, 0, 0, 0.2);
  padding: 1rem 0;
}

/* Product Detail Page */
.product-detail-img {
  border-radius: 0.5rem;
  box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.1);
  max-height: 400px;
  width: 100%;
  object-fit: contain;
  background-color: #f8f9fc;
  padding: 15px;
}

.product-detail-title {
  font-weight: 700;
  margin-bottom: 1rem;
}

.product-detail-price {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--danger-color);
  margin-bottom: 1rem;
}

.product-detail-category {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  background-color: var(--primary-color);
  color: var(--white-color);
  border-radius: 0.25rem;
  font-size: 0.875rem;
  margin-bottom: 1rem;
}

.product-image-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 400px;
}

/* Google Maps Styling */
.map-container {
  position: relative;
  overflow: hidden;
  padding-top: 56.25%; /* 16:9 Aspect Ratio */
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.google-map {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
}

/* Specific styling for footer map */
#footer-map-container {
  border-radius: 10px;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  margin-bottom: 30px;
}

/* Cart and Checkout Styles */
.cart-item-image {
  width: 80px;
  height: 80px;
  object-fit: contain;
  border-radius: 0.25rem;
}

.cart-quantity-input {
  width: 80px;
  text-align: center;
}

.cart-total-card {
  border: 2px solid var(--primary-color);
  border-radius: 0.5rem;
}

.cart-total-card .card-header {
  background: linear-gradient(to right, var(--primary-color), #224abe);
  color: var(--white-color);
  font-weight: 700;
}

.checkout-form .form-control:focus {
  border-color: var(--secondary-color);
  box-shadow: 0 0 0 0.2rem rgba(28, 200, 138, 0.25);
}

.order-summary-item {
  border-bottom: 1px solid var(--light-gray);
  padding-bottom: 0.5rem;
  margin-bottom: 0.5rem;
}

.order-summary-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

/* Badge positioning for cart icon */
.nav-link .badge {
  font-size: 0.7rem;
  padding: 0.25em 0.4em;
  border-radius: 50%;
  min-width: 1.5em;
  text-align: center;
}

/* Timeline styles for order success */
.timeline {
  position: relative;
  padding-left: 30px;
}

.timeline-item {
  position: relative;
  margin-bottom: 20px;
}

.timeline-item:not(:last-child)::before {
  content: '';
  position: absolute;
  left: -19px;
  top: 30px;
  width: 2px;
  height: calc(100% + 10px);
  background-color: #dee2e6;
}

.timeline-marker {
  position: absolute;
  left: -30px;
  top: 0;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
}

.timeline-content {
  padding-left: 15px;
}

.timeline-item.active .timeline-marker {
  background-color: #28a745 !important;
}

/* Responsive improvements */
@media (max-width: 768px) {
  .cart-quantity-input {
    width: 60px;
  }

  .cart-item-image {
    width: 60px;
    height: 60px;
  }

  .product-card .card-body {
    padding: 1rem;
  }

  .btn-group-vertical .btn {
    margin-bottom: 0.25rem;
  }
}