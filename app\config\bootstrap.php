<?php
// Configure session settings before starting the session
ini_set('session.cookie_httponly', 1);
ini_set('session.use_only_cookies', 1);
ini_set('session.cookie_secure', 0); // Set to 1 if using HTTPS

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Define base paths if not already defined
if (!defined('SITE_ROOT')) {
    define('SITE_ROOT', dirname(dirname(__DIR__))); // Points to project root
}

if (!defined('APP_ROOT')) {
    define('APP_ROOT', dirname(__DIR__)); // Points to app folder
}

// Load configuration files
require_once APP_ROOT . '/config/config.php';
require_once APP_ROOT . '/config/database.php';

// Autoload function
spl_autoload_register(function ($class_name) {
    $paths = [
        APP_ROOT . '/controllers/',
        APP_ROOT . '/models/',
        APP_ROOT . '/helper/'
    ];
    
    foreach ($paths as $path) {
        $file = $path . $class_name . '.php';
        if (file_exists($file)) {
            require_once $file;
            return;
        }
    }
});
