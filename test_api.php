<?php
/**
 * Simple API Test Script
 * Run this script to test basic API functionality
 */

// Configuration
$baseUrl = 'http://localhost:8080/api/v1';
$testData = [
    'username' => 'admin',
    'password' => 'admin123'
];

// Check if we can reach the server
echo "Checking server connectivity...\n";
$testConnection = makeRequest($baseUrl . '/categories', 'GET');
if (isset($testConnection['error'])) {
    echo "ERROR: Cannot connect to server. Please make sure:\n";
    echo "1. Your server is running on http://localhost:8080\n";
    echo "2. The API endpoints are accessible\n";
    echo "Error: " . $testConnection['error'] . "\n";
    exit(1);
}

/**
 * Make HTTP request
 */
function makeRequest($url, $method = 'GET', $data = null, $headers = []) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    // Set method
    switch (strtoupper($method)) {
        case 'POST':
            curl_setopt($ch, CURLOPT_POST, true);
            break;
        case 'PUT':
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
            break;
        case 'DELETE':
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
            break;
    }
    
    // Set data
    if ($data) {
        if (is_array($data)) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            $headers[] = 'Content-Type: application/json';
        } else {
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        }
    }
    
    // Set headers
    if (!empty($headers)) {
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    curl_close($ch);
    
    if ($error) {
        return ['error' => $error];
    }
    
    return [
        'status_code' => $httpCode,
        'body' => $response,
        'data' => json_decode($response, true)
    ];
}

/**
 * Test function
 */
function runTest($testName, $url, $method = 'GET', $data = null, $headers = []) {
    echo "\n" . str_repeat("=", 50) . "\n";
    echo "Testing: $testName\n";
    echo "URL: $url\n";
    echo "Method: $method\n";
    
    if ($data) {
        echo "Data: " . json_encode($data, JSON_PRETTY_PRINT) . "\n";
    }
    
    $result = makeRequest($url, $method, $data, $headers);
    
    if (isset($result['error'])) {
        echo "ERROR: " . $result['error'] . "\n";
        return null;
    }
    
    echo "Status Code: " . $result['status_code'] . "\n";
    echo "Response: " . json_encode($result['data'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
    
    return $result;
}

// Start testing
echo "Starting API Tests...\n";
echo "Base URL: $baseUrl\n";

$token = null;

// Test 1: Login
$loginResult = runTest(
    'User Login',
    "$baseUrl/auth/login",
    'POST',
    $testData
);

if ($loginResult && $loginResult['data']['success']) {
    $token = $loginResult['data']['data']['token'];
    echo "Login successful! Token obtained.\n";
} else {
    echo "Login failed! Cannot continue with authenticated tests.\n";
}

// Test 2: Get All Categories
runTest(
    'Get All Categories',
    "$baseUrl/categories"
);

// Test 3: Get All Products
runTest(
    'Get All Products',
    "$baseUrl/products"
);

// Test 4: Get Products with pagination
runTest(
    'Get Products with Pagination',
    "$baseUrl/products?page=1&limit=5"
);

// Test 5: Get Single Product
runTest(
    'Get Single Product',
    "$baseUrl/products/1"
);

// Test 6: Get Profile (if logged in)
if ($token) {
    runTest(
        'Get User Profile',
        "$baseUrl/auth/profile",
        'GET',
        null,
        ["Authorization: Bearer $token"]
    );
}

// Test 7: Create Order
runTest(
    'Create Order',
    "$baseUrl/orders",
    'POST',
    [
        'customer_name' => 'Test Customer',
        'customer_phone' => '0123456789',
        'customer_address' => '123 Test Street, Test City',
        'items' => [
            [
                'product_id' => 1,
                'quantity' => 2
            ]
        ]
    ]
);

// Test 8: Search Orders by Phone
runTest(
    'Search Orders by Phone',
    "$baseUrl/orders/search",
    'POST',
    [
        'phone' => '0123456789'
    ]
);

// Test 9: Get All Orders (Admin only)
if ($token) {
    runTest(
        'Get All Orders (Admin)',
        "$baseUrl/orders",
        'GET',
        null,
        ["Authorization: Bearer $token"]
    );
}

// Test 10: Create Category (Admin only)
if ($token) {
    runTest(
        'Create Category (Admin)',
        "$baseUrl/categories",
        'POST',
        [
            'name' => 'Test Category API',
            'description' => 'Category created via API test'
        ],
        ["Authorization: Bearer $token"]
    );
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "API Tests Completed!\n";
echo "\nTo run this test:\n";
echo "1. Make sure your server is running on http://localhost:8080\n";
echo "2. Make sure you have admin account with username 'admin' and password 'admin123'\n";
echo "3. Run: php test_api.php\n";
?>
