<?php 
require_once 'app/helper/SessionHelper.php';
include 'app/views/shares/header.php'; ?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="mb-4">
    <ol class="breadcrumb bg-light py-2 px-3 rounded shadow-sm">
        <li class="breadcrumb-item"><a href="/shopbanhang/Category/"><i class="fas fa-home"></i> Trang chủ</a></li>
        <li class="breadcrumb-item active" aria-current="page">Chi tiết danh mục</li>
    </ol>
</nav>

<?php if ($category): ?>
    <div class="card shadow-lg border-0 mb-5">
        <div class="card-header text-center">
            <h2 class="mb-0 category-detail-title">Chi tiết danh mục</h2>
        </div>
        <div class="card-body p-4 p-lg-5">
            <div class="row">
                <div class="col-md-12">
                    <h1 class="category-detail-title mb-4">
                        <i class="fas fa-folder mr-2"></i>
                        <?php echo htmlspecialchars($category->name, ENT_QUOTES, 'UTF-8'); ?>
                    </h1>

                    <div class="mb-4">
                        <h5 class="mb-3"><i class="fas fa-info-circle mr-2"></i>Mô tả danh mục</h5>
                        <div class="p-3 bg-light rounded">
                            <?php echo nl2br(htmlspecialchars($category->description, ENT_QUOTES, 'UTF-8')); ?>
                        </div>
                    </div>

                    <div class="mt-4">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <?php if (SessionHelper::isAdmin()): ?>
                                    <a href="/shopbanhang/Category/edit/<?php echo $category->id; ?>" class="btn btn-warning">
                                        <i class="fas fa-edit mr-1"></i>Sửa danh mục
                                    </a>
                                    <a href="/shopbanhang/Category/delete/<?php echo $category->id; ?>" 
                                       class="btn btn-danger"
                                       onclick="return confirm('Bạn có chắc chắn muốn xóa danh mục này?');">
                                        <i class="fas fa-trash-alt mr-1"></i>Xóa danh mục
                                    </a>
                                <?php endif; ?>
                            </div>
                            <div>
                                <a href="/shopbanhang/Category" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left mr-1"></i>Quay lại danh sách
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php else: ?>
    <div class="alert alert-danger text-center p-5 shadow-sm">
        <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
        <h4>Không tìm thấy danh mục!</h4>
        <p>Danh mục bạn đang tìm kiếm không tồn tại hoặc đã bị xóa.</p>
        <a href="/shopbanhang/Category/" class="btn btn-primary mt-3">
            <i class="fas fa-home mr-2"></i>Quay lại trang chủ
        </a>
    </div>
<?php endif; ?>

<?php include 'app/views/shares/footer.php'; ?>
