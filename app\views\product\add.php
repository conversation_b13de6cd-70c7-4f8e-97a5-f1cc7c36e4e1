<?php include 'app/views/shares/header.php'; ?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="mb-4">
    <ol class="breadcrumb bg-light py-2 px-3 rounded shadow-sm">
        <li class="breadcrumb-item"><a href="/shopbanhang/Product/"><i class="fas fa-home"></i> Trang chủ</a></li>
        <li class="breadcrumb-item active" aria-current="page">Thêm sản phẩm mới</li>
    </ol>
</nav>

<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card shadow-lg border-0 mb-5">
            <div class="card-header text-center">
                <h2 class="mb-0"><i class="fas fa-plus-circle mr-2"></i>Thêm sản phẩm mới</h2>
            </div>
            <div class="card-body p-4 p-lg-5">
                <?php if (!empty($errors)): ?>
                    <div class="alert alert-danger">
                        <h5 class="alert-heading"><i class="fas fa-exclamation-triangle mr-2"></i>Lỗi!</h5>
                        <ul class="mb-0">
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo htmlspecialchars($error, ENT_QUOTES, 'UTF-8'); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <form method="POST" action="/shopbanhang/Product/save" enctype="multipart/form-data" onsubmit="return validateForm();">
                    <div class="form-group">
                        <label for="name"><i class="fas fa-tag mr-2"></i>Tên sản phẩm:</label>
                        <input type="text" id="name" name="name" class="form-control" placeholder="Nhập tên sản phẩm" required>
                    </div>

                    <div class="form-group">
                        <label for="description"><i class="fas fa-align-left mr-2"></i>Mô tả:</label>
                        <textarea id="description" name="description" class="form-control" rows="5" placeholder="Nhập mô tả chi tiết về sản phẩm" required></textarea>
                    </div>

                    <div class="form-group">
                        <label for="price"><i class="fas fa-money-bill-wave mr-2"></i>Giá:</label>
                        <div class="input-group">
                            <input type="number" id="price" name="price" class="form-control" step="0.01" placeholder="Nhập giá sản phẩm" required>
                            <div class="input-group-append">
                                <span class="input-group-text">VND</span>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="category_id"><i class="fas fa-folder mr-2"></i>Danh mục:</label>
                        <select id="category_id" name="category_id" class="form-control" required>
                            <option value="" disabled selected>-- Chọn danh mục --</option>
                            <?php foreach ($categories as $category): ?>
                                <option value="<?php echo $category->id; ?>">
                                    <?php echo htmlspecialchars($category->name, ENT_QUOTES, 'UTF-8'); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="image"><i class="fas fa-image mr-2"></i>Hình ảnh:</label>
                        <div class="custom-file">
                            <input type="file" class="custom-file-input" id="image" name="image">
                            <label class="custom-file-label" for="image">Chọn hình ảnh</label>
                        </div>
                        <small class="form-text text-muted">Hỗ trợ các định dạng: JPG, JPEG, PNG, GIF và WEBP</small>
                    </div>

                    <div class="form-group mt-5 text-center">
                        <button type="submit" class="btn btn-primary btn-lg px-5">
                            <i class="fas fa-save mr-2"></i>Lưu sản phẩm
                        </button>
                        <a href="/shopbanhang/Product/list" class="btn btn-secondary btn-lg px-5 ml-2">
                            <i class="fas fa-arrow-left mr-2"></i>Quay lại
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for file input -->
<script>
    // Show file name when selected
    document.querySelector('.custom-file-input').addEventListener('change', function(e) {
        var fileName = e.target.files[0].name;
        var nextSibling = e.target.nextElementSibling;
        nextSibling.innerText = fileName;
    });
</script>

<?php include 'app/views/shares/footer.php'; ?>