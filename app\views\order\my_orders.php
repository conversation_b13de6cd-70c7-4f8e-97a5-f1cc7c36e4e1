<?php include 'app/views/shares/header.php'; ?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/shopbanhang/Product/">Trang chủ</a></li>
        <li class="breadcrumb-item active" aria-current="page">Đơn hàng của tôi</li>
    </ol>
</nav>

<!-- Flash Messages -->
<?php if (isset($_SESSION['flash'])): ?>
    <div class="alert alert-<?php echo $_SESSION['flash']['type'] === 'error' ? 'danger' : 'success'; ?> alert-dismissible fade show" role="alert">
        <?php echo $_SESSION['flash']['message']; ?>
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <?php unset($_SESSION['flash']); ?>
<?php endif; ?>

<div class="card">
    <div class="card-header">
        <h4 class="mb-0"><i class="fas fa-shopping-bag mr-2"></i>Đơn hàng của tôi</h4>
    </div>    <div class="card-body">

        <?php if (isset($orders['orders']) && !empty($orders['orders'])): ?>
        <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Mã đơn</th>
                            <th>Khách hàng</th>
                            <th>Địa chỉ</th>
                            <th>Tổng tiền</th>
                            <th>Ngày đặt</th>
                            <th>Trạng thái</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($orders['orders'] as $order): ?>
                            <tr>
                                <td>#<?php echo $order['id']; ?></td>
                                <td><?php echo htmlspecialchars($order['name']); ?></td>
                                <td><?php echo htmlspecialchars($order['address']); ?></td>
                                <td><?php echo number_format($order['total_amount'], 0, ',', '.'); ?>đ</td>
                                <td><?php echo date('d/m/Y H:i', strtotime($order['created_at'])); ?></td>
                                <td style="min-width: 150px;">
                                    <?php
                                        $statusClasses = [
                                            'pending' => 'badge-warning',
                                            'processing' => 'badge-info',
                                            'shipping' => 'badge-primary',
                                            'completed' => 'badge-success',
                                            'cancelled' => 'badge-danger'
                                        ];
                                        $statusLabels = [
                                            'pending' => 'Chờ xử lý',
                                            'processing' => 'Đang xử lý',
                                            'shipping' => 'Đang giao hàng',
                                            'completed' => 'Đã hoàn thành',
                                            'cancelled' => 'Đã hủy'
                                        ];
                                    ?>
                                    <span class="badge <?php echo $statusClasses[$order['status']] ?? 'badge-secondary'; ?>">
                                        <?php echo $statusLabels[$order['status']] ?? 'Không xác định'; ?>
                                    </span>
                                </td>
                                <td>
                                    <a href="/shopbanhang/Order/detail/<?php echo $order['id']; ?>" 
                                       class="btn btn-info btn-sm">
                                        <i class="fas fa-eye mr-1"></i>Chi tiết
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Phân trang -->
            <?php if ($orders['total_pages'] > 1): ?>                <nav aria-label="Page navigation" class="mt-4">
                    <ul class="pagination justify-content-center">
                        <?php for ($i = 1; $i <= $orders['total_pages']; $i++): ?>
                            <li class="page-item <?php echo $i === $orders['page'] ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                        <?php endfor; ?>
                    </ul>
                </nav>
            <?php endif; ?>        <?php elseif (isset($orders)): ?>
            <div class="alert alert-info">
                <i class="fas fa-info-circle mr-2"></i>Bạn chưa có đơn hàng nào.
            </div>
        <?php endif; ?>
    </div>
</div>

<?php include 'app/views/shares/footer.php'; ?>
